import React from 'react';
import {Text, StyleSheet, View} from 'react-native';
// import Colors from '../../theme/colors';
import {useTipThemeColors} from './hooks/useTipThemeColors';

interface TipSubtitleProps {
  subtitle: string;
}

const TipSubtitle: React.FC<TipSubtitleProps> = ({subtitle}) => {
  const {
    textColor,
    secondaryColor,
    surfaceColor,
    borderColor,
    accentLineShadowColor,
    accentLineShadowOpacity,
    accentLineShadowRadius,
    accentLineElevation,
    subtitleWrapperShadowColor,
    subtitleWrapperShadowOpacity,
    subtitleWrapperShadowRadius,
    subtitleWrapperElevation,
    tipSubtitleTextShadowColor,
    tipSubtitleTextShadowRadius,
    tipSubtitleTextShadowOffset,
  } = useTipThemeColors();

  return (
    <View style={styles.subtitleContainer}>
      <View
        style={[
          styles.subtitleWrapper,
          {backgroundColor: surfaceColor, borderColor},
          subtitleWrapperShadowColor && {
            shadowColor: subtitleWrapperShadowColor,
            shadowOffset: {width: 0, height: 0},
            shadowOpacity: subtitleWrapperShadowOpacity,
            shadowRadius: subtitleWrapperShadowRadius,
            elevation: subtitleWrapperElevation,
          },
        ]}>
        <View
          style={[
            styles.accentLine,
            {backgroundColor: secondaryColor},
            accentLineShadowColor && {
              shadowColor: accentLineShadowColor,
              shadowOffset: {width: 0, height: 0},
              shadowOpacity: accentLineShadowOpacity,
              shadowRadius: accentLineShadowRadius,
              elevation: accentLineElevation,
            },
          ]}
        />
        <Text
          style={[
            styles.tipSubtitle,
            {color: textColor},
            tipSubtitleTextShadowColor && {
              textShadowColor: tipSubtitleTextShadowColor,
              textShadowRadius: tipSubtitleTextShadowRadius,
              textShadowOffset: tipSubtitleTextShadowOffset,
            },
          ]}>
          {subtitle}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  subtitleContainer: {
    marginBottom: 20,
  },
  subtitleWrapper: {
    borderRadius: 8,
    padding: 12,
    paddingLeft: 16,
    position: 'relative',
    borderLeftWidth: 0,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  accentLine: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 4,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  tipSubtitle: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'left',
    lineHeight: 24,
    letterSpacing: 0.3,
  },
});

export default TipSubtitle;
