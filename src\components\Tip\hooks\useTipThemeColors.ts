import {useTheme} from '../../../theme/ThemeContext';
import Colors from '../../../theme/colors';

export function useTipThemeColors() {
  const {isDarkMode, isNeonMode, isSynthwaveMode} = useTheme();

  if (isSynthwaveMode) {
    return {
      // Text colors
      textColor: Colors.synthwave.text,
      textSecondaryColor: Colors.synthwave.textSecondary,
      
      // Background colors
      surfaceColor: Colors.synthwave.surface,
      cardBgColor: Colors.synthwave.cardBg,
      imageBackgroundColor: Colors.synthwave.surface,
      
      // Border colors
      borderColor: Colors.synthwave.border,
      imageBorderColor: Colors.synthwave.border,
      detailsBorderTopColor: Colors.synthwave.border,
      
      // Accent colors
      accentColor: Colors.synthwave.accent,
      secondaryColor: Colors.synthwave.secondary,
      primaryColor: Colors.synthwave.primary,
      
      // Glow and shadow effects
      imageGlowColor: Colors.synthwave.glow.pink,
      menuGlowColor: Colors.synthwave.glow.pink,
      accentLineShadowColor: Colors.synthwave.glow.pink,
      subtitleWrapperShadowColor: Colors.synthwave.glow.pink,
      tipSubtitleTextShadowColor: Colors.synthwave.glow.pink,
      
      // Shadow properties
      menuIndicatorShadowOpacity: 0.7,
      menuIndicatorShadowRadius: 5,
      menuIndicatorElevation: 5,
      accentLineShadowOpacity: 0.8,
      accentLineShadowRadius: 5,
      accentLineElevation: 5,
      subtitleWrapperShadowOpacity: 0.5,
      subtitleWrapperShadowRadius: 4,
      subtitleWrapperElevation: 4,
      tipSubtitleTextShadowRadius: 3,
      tipSubtitleTextShadowOffset: {width: 0, height: 0},
    };
  } else if (isNeonMode) {
    return {
      // Text colors
      textColor: Colors.neon.text,
      textSecondaryColor: Colors.neon.textSecondary,
      
      // Background colors
      surfaceColor: Colors.neon.surface,
      cardBgColor: Colors.neon.cardBg,
      imageBackgroundColor: Colors.neon.surface,
      
      // Border colors
      borderColor: Colors.neon.border,
      imageBorderColor: Colors.neon.border,
      detailsBorderTopColor: Colors.neon.border,
      
      // Accent colors
      accentColor: Colors.neon.accent,
      secondaryColor: Colors.neon.secondary,
      primaryColor: Colors.neon.primary,
      
      // Glow and shadow effects
      imageGlowColor: Colors.neon.glow.blue,
      menuGlowColor: Colors.neon.glow.blue,
      accentLineShadowColor: Colors.neon.glow.blue,
      subtitleWrapperShadowColor: Colors.neon.glow.blue,
      tipSubtitleTextShadowColor: Colors.neon.glow.blue,
      
      // Shadow properties
      menuIndicatorShadowOpacity: 0.8,
      menuIndicatorShadowRadius: 4,
      menuIndicatorElevation: 4,
      accentLineShadowOpacity: 0.8,
      accentLineShadowRadius: 4,
      accentLineElevation: 4,
      subtitleWrapperShadowOpacity: 0.5,
      subtitleWrapperShadowRadius: 3,
      subtitleWrapperElevation: 3,
      tipSubtitleTextShadowRadius: 3,
      tipSubtitleTextShadowOffset: {width: 0, height: 0},
    };
  } else {
    return {
      // Text colors
      textColor: isDarkMode ? Colors.dark.text : Colors.light.text,
      textSecondaryColor: isDarkMode ? Colors.dark.textSecondary : Colors.light.textSecondary,
      
      // Background colors
      surfaceColor: isDarkMode ? Colors.dark.surface : Colors.light.surface,
      cardBgColor: isDarkMode ? Colors.dark.cardBg : Colors.light.cardBg,
      imageBackgroundColor: isDarkMode ? Colors.dark.surface : Colors.light.surface,
      
      // Border colors
      borderColor: isDarkMode ? Colors.dark.border : Colors.light.border,
      imageBorderColor: isDarkMode ? Colors.dark.border : Colors.light.border,
      detailsBorderTopColor: isDarkMode ? Colors.dark.border : Colors.light.border,
      
      // Accent colors
      accentColor: isDarkMode ? Colors.dark.accent : Colors.light.accent,
      secondaryColor: isDarkMode ? Colors.dark.secondary : Colors.light.secondary,
      primaryColor: isDarkMode ? Colors.dark.primary : Colors.light.primary,
      
      // Glow and shadow effects
      imageGlowColor: undefined,
      menuGlowColor: undefined,
      accentLineShadowColor: undefined,
      subtitleWrapperShadowColor: undefined,
      tipSubtitleTextShadowColor: undefined,
      
      // Shadow properties
      menuIndicatorShadowOpacity: undefined,
      menuIndicatorShadowRadius: undefined,
      menuIndicatorElevation: undefined,
      accentLineShadowOpacity: undefined,
      accentLineShadowRadius: undefined,
      accentLineElevation: undefined,
      subtitleWrapperShadowOpacity: undefined,
      subtitleWrapperShadowRadius: undefined,
      subtitleWrapperElevation: undefined,
      tipSubtitleTextShadowRadius: undefined,
      tipSubtitleTextShadowOffset: undefined,
    };
  }
} 