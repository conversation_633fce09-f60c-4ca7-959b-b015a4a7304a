import sqlite3 from 'sqlite3';
import {runStmt, finalizeStmt} from './databaseUtils.ts';

export const insertUserSettings = async (db: sqlite3.Database) => {
  console.log('Inserting user settings...');
  const userSettingsStmt = db.prepare(
    'INSERT INTO UserSettings (fontSize, showWelcomeScreen, theme) VALUES (?, ?, ?)',
  );
  try {
    await runStmt(userSettingsStmt, ['medium', 1, 'system']);
  } catch (runErr: any) {
    console.error('Error inserting user settings:', runErr.message);
    // Optionally, increment an error counter for user settings if needed
  }
  await finalizeStmt(userSettingsStmt);
  console.log('Finished inserting user settings.');
}; // Insert UserSettings
