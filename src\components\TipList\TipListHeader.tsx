import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useTipListThemeColors} from './hooks/useTipListThemeColors';

interface TipListHeaderProps {
  onBackPress: () => void;
  borderColor?: string;
}

const TipListHeader: React.FC<TipListHeaderProps> = ({onBackPress}) => {
  const colors = useTipListThemeColors();

  return (
    <View style={[styles.header, {borderBottomColor: colors.borderColor}]}>
      <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
        <Text
          style={[
            styles.backButtonText,
            {
              color: colors.backButtonTextColor,
              ...colors.backButtonTextShadow,
            },
          ]}>
          ← Back
        </Text>
      </TouchableOpacity>
      <View style={styles.titleContainer}>
        <Text
          style={[
            styles.headerTitle,
            {
              color: colors.headerTextColor,
              ...colors.titleTextShadow,
            },
          ]}>
          All Tips
        </Text>
      </View>
      <View style={styles.rightSpacer} />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 0,
    paddingVertical: 4,
    paddingHorizontal: 0,
    flex: 1,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  rightSpacer: {
    flex: 1,
  },
});

export default TipListHeader;
