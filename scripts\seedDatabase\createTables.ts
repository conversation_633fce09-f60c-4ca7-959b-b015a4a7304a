import sqlite3 from 'sqlite3';
import {runDb} from './databaseUtils.ts';

export async function createTables(db: sqlite3.Database): Promise<void> {
  console.log('Creating tables...');

  // Create the Details table (must be created before Tips due to foreign key)
  await runDb(
    `
    CREATE TABLE Details (
      id TEXT PRIMARY KEY,
      url TEXT,
      summary TEXT,
      graph TEXT
    )
  `,
    db,
  );
  console.log("Table 'Details' created successfully.");

  // Create the Tips table
  await runDb(
    `
    CREATE TABLE Tips (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT,
      subtitle TEXT,
      url TEXT,
      img TEXT,
      detail_id TEXT,
      FOREIGN KEY (detail_id) REFERENCES Details(id)
    )
  `,
    db,
  );
  console.log("Table 'Tips' created successfully.");

  // Create the Tips table
  await runDb(
    `
    CREATE TABLE UserSettings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      fontSize TEXT,
      showWelcomeScreen INTEGER,
      theme TEXT
    )
  `,
    db,
  );
  console.log("Table 'UserSettings' created successfully.");

  console.log('Finished creating tables.');
}
