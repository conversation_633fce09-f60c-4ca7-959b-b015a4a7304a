import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useWelcomeScreenThemeColors} from './hooks/useWelcomeScreenThemeColors';

const WelcomeScreenDescription = (): React.JSX.Element => {
  const colors = useWelcomeScreenThemeColors();

  return (
    <View style={styles.descriptionSection}>
      <Text
        style={[
          styles.descriptionText,
          {color: colors.textSecondaryColor},
        ]}>
        {' '}
        {/* synthwave.textSecondary */}
        Get expert-backed tips and guidance throughout your pregnancy. From
        nutrition and exercise to prenatal care and wellness, we're here to
        support you every step of the way.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  descriptionSection: {
    marginBottom: 40,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
});

export default WelcomeScreenDescription;
