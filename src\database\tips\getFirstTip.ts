import {ResultSet, Transaction} from 'react-native-sqlite-storage';
import {db} from '../database';
import {TipWithDetails, Detail} from '../../models/types';

// Helper function to map raw DB result to TipWithDetails
const mapRowToTipWithDetails = (row: any): TipWithDetails | null => {
  if (!row) {
    return null;
  }
  const details: Detail | null = row.detail_id
    ? {
        id: row.detail_id,
        url: row.detailUrl,
        summary: row.summary,
        graph: row.graph,
      }
    : null;

  return {
    id: row.id,
    title: row.title,
    subtitle: row.subtitle,
    url: row.url,
    img: row.img || '',
    details: details,
  };
};

export const getFirstTip = (): Promise<TipWithDetails | null> => {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.transaction((tx: Transaction) => {
      const query = `
        SELECT
          Tips.*,
          Details.id AS detail_id, Details.summary, Details.graph, Details.url AS detailUrl
        FROM Tips
        LEFT JOIN Details ON Tips.detail_id = Details.id
        ORDER BY Tips.id ASC
        LIMIT 1;
      `;
      tx.executeSql(
        query,
        [],
        (_, results: ResultSet) => {
          if (results.rows.length > 0) {
            const rawTip = results.rows.item(0);
            const formattedTip = mapRowToTipWithDetails(rawTip);
            console.log('First tip with detail:', formattedTip);
            resolve(formattedTip);
          } else {
            resolve(null);
          }
        },
        (_, error: any) => {
          console.error('Error fetching first tip:', error);
          reject(error);
          return false;
        }
      );
    });
  });
};
