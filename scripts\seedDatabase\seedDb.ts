import sqlite3 from 'sqlite3'; // Changed import style
import * as fs from 'fs';
import * as path from 'path';
import {fileURLToPath} from 'url'; // Import necessary function
import {dirname} from 'path'; // Import necessary function
import {dropTables} from './dropTables.ts';
import {createTables} from './createTables.ts'; // Import the new function
import {insertDetails} from './insertDetails.ts'; // Import the new function
import {closeDb, runDb} from './databaseUtils.ts'; // Import closeDb
import {insertUserSettings} from './insertUserSettings.ts';
import {insertTips} from './insertTips.ts';

// Get current directory in ES module scope
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Define paths relative to the project root
const projectRoot = path.resolve(__dirname, '..', '..'); // Adjust based on script location
const dbPath = path.join(
  projectRoot,
  'android',
  'app',
  'src',
  'main',
  'assets',
  'www',
  'TipsDatabase.db',
);

// Ensure the database directory exists (optional, depends on setup)
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, {recursive: true});
  console.log(`Created directory: ${dbDir}`);
}

const db = new sqlite3.Database(dbPath, async err => {
  // Make callback async
  if (err) {
    console.error('Error opening database:', err.message);
    return;
  }
  console.log(`Connected to the database at ${dbPath}`);

  // Use serialize to ensure sequential execution of commands within this block
  db.serialize(async () => {
    // Make serialize callback async
    try {
      await dropTables(db);
      await createTables(db);
      await runDb('BEGIN TRANSACTION;', db);
      console.log('Transaction started.');
      await insertDetails(db);
      await insertUserSettings(db);
      await insertTips(db);
      await runDb('COMMIT;', db);
      console.log('Transaction committed successfully.');
      closeDb(db, 'Database seeded successfully.');
    } catch (error: any) {
      console.error('Error during database seeding:', error.message);
      // Rollback transaction in case of error
      try {
        await runDb('ROLLBACK;', db);
        console.log('Transaction rolled back.');
      } catch (rollbackErr: any) {
        console.error('Error rolling back transaction:', rollbackErr.message);
      }
      closeDb(db, 'Database seeding failed.');
    }
  });
});
