import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useWelcomeScreenThemeColors} from './hooks/useWelcomeScreenThemeColors';

const WelcomeScreenHeader = (): React.JSX.Element => {
  const colors = useWelcomeScreenThemeColors();

  return (
    <View style={styles.headerSection}>
      <View style={styles.iconContainer}>
        <Text
          style={[
            styles.appIcon,
            {color: colors.primaryColor},
            (colors.textShadowRadius !== undefined) && {
              color: colors.primaryColor,
              textShadowColor: colors.iconGlowColor,
              textShadowOffset: colors.textShadowOffset,
              textShadowRadius: colors.textShadowRadius,
            },
          ]}>
          🤱
        </Text>
      </View>

      <Text
        style={[
          styles.appTitle,
          {color: colors.textColor},
          (colors.textShadowRadius !== undefined) && {
            textShadowColor: colors.textGlowColor,
            textShadowOffset: colors.textShadowOffset,
            textShadowRadius: colors.textShadowRadius,
          },
        ]}>
        Pregnancy Tips
      </Text>

      <Text
        style={[
          styles.appSubtitle,
          {color: colors.textSecondaryColor},
        ]}>
        Your daily companion for a healthy pregnancy journey
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  headerSection: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 32,
  },
  iconContainer: {
    marginBottom: 16,
  },
  appIcon: {
    fontSize: 80,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  appSubtitle: {
    fontSize: 18,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default WelcomeScreenHeader;
