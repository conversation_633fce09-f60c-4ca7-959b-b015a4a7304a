import React, {useRef} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Text,
} from 'react-native';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import Tip, {TipRef} from '../components/Tip/Tip';
import ExpandableMenu from '../components/ExpandableMenu/ExpandableMenu';
import Colors from '../theme/colors';
import {useTheme} from '../theme/ThemeContext';

interface MainScreenProps {
  initialTipId?: number | null;
}

const MainScreen = ({
  initialTipId,
}: MainScreenProps = {}): React.JSX.Element => {
  const {isDarkMode, isNeonMode, isSynthwaveMode} = useTheme();
  const tipRef = useRef<TipRef>(null);
  const insets = useSafeAreaInsets();

  const handleNextTip = () => {
    tipRef.current?.handleNext();
  };

  const handlePreviousTip = () => {
    tipRef.current?.handlePrevious();
  };

  // Get colors based on the active theme
  let backgroundColor, textColor, primaryColor, secondaryColor, borderColor;
  let headerTextShadow = {};
  let previousButtonTextShadow = {};
  let nextButtonTextShadow = {};
  let previousButtonShadow = {};
  let nextButtonShadow = {};

  if (isNeonMode) {
    backgroundColor = Colors.neon.background;
    textColor = Colors.neon.text;
    primaryColor = Colors.neon.primary;
    secondaryColor = Colors.neon.secondary;
    borderColor = Colors.neon.border;
    headerTextShadow = {
      textShadowColor: primaryColor,
      textShadowRadius: 5,
      textShadowOffset: {width: 0, height: 0},
    };
    previousButtonTextShadow = {
      textShadowColor: secondaryColor,
      textShadowRadius: 5,
    };
    nextButtonTextShadow = {
      textShadowColor: primaryColor,
      textShadowRadius: 5,
    };
    previousButtonShadow = {
      shadowColor: secondaryColor,
      shadowOpacity: 0.8,
      shadowRadius: 10,
    };
    nextButtonShadow = {
      shadowColor: primaryColor,
      shadowOpacity: 0.8,
      shadowRadius: 10,
    };
  } else if (isSynthwaveMode) {
    backgroundColor = Colors.synthwave.background;
    textColor = Colors.synthwave.text;
    primaryColor = Colors.synthwave.primary;
    secondaryColor = Colors.synthwave.secondary;
    borderColor = Colors.synthwave.border;
    headerTextShadow = {
      textShadowColor: Colors.synthwave.glow.pink,
      textShadowRadius: 8,
      textShadowOffset: {width: 0, height: 0},
    };
    previousButtonTextShadow = {
      textShadowColor: Colors.synthwave.glow.blue,
      textShadowRadius: 0,
    };
    nextButtonTextShadow = {
      textShadowColor: Colors.synthwave.glow.pink,
      textShadowRadius: 0,
    };
    previousButtonShadow = {
      shadowColor: Colors.synthwave.glow.blue,
      shadowOpacity: 0.8,
      shadowRadius: 10,
    };
    nextButtonShadow = {
      shadowColor: Colors.synthwave.glow.pink,
      shadowOpacity: 0.8,
      shadowRadius: 15,
    };
  } else {
    backgroundColor = isDarkMode
      ? Colors.dark.background
      : Colors.light.background;
    textColor = isDarkMode ? Colors.dark.text : Colors.light.text;
    primaryColor = isDarkMode ? Colors.dark.primary : Colors.light.primary;
    secondaryColor = isDarkMode
      ? Colors.dark.secondary
      : Colors.light.secondary;
    borderColor = isDarkMode ? Colors.dark.border : Colors.light.border;
    // Default shadows for light/dark mode
    previousButtonShadow = {
      shadowColor: '#000',
      shadowOpacity: 0.1,
      shadowRadius: 4,
    };
    nextButtonShadow = {
      shadowColor: '#000',
      shadowOpacity: 0.1,
      shadowRadius: 4,
    };
  }

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor}]}
      edges={['left', 'right', 'top']}>
      <View style={[styles.header, {borderBottomColor: borderColor}]}>
        <View style={styles.menuButtonContainer}>
          <ExpandableMenu />
        </View>
        <Text
          style={[styles.headerTitle, {color: textColor}, headerTextShadow]}>
          Daily Tips
        </Text>
      </View>
      <ScrollView
        style={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}>
        <Tip ref={tipRef} onNext={() => {}} initialTipId={initialTipId} />
      </ScrollView>
      <View
        style={[
          styles.footer,
          {
            paddingBottom: Math.max(20, insets.bottom),
            borderTopColor: borderColor,
          },
        ]}>
        <View style={styles.buttonsRow}>
          <TouchableOpacity
            style={[
              styles.button,
              {backgroundColor: secondaryColor},
              previousButtonShadow,
            ]}
            onPress={handlePreviousTip}
            activeOpacity={0.8}>
            <Text style={[styles.buttonText, previousButtonTextShadow]}>
              Previous
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.button,
              {backgroundColor: primaryColor},
              nextButtonShadow,
            ]}
            onPress={handleNextTip}
            activeOpacity={0.8}>
            <Text style={[styles.buttonText, nextButtonTextShadow]}>
              Next Tip
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0', // This will be overridden in the component
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuButtonContainer: {
    position: 'absolute',
    left: 20,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '700',
    textAlign: 'center',
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 30,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  buttonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    width: '48%',
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MainScreen;
