import {MenuOption} from '../components/ExpandableMenu/ExpandableMenuOption';

export const DATABASE_NAME = 'TipsDatabase.db';

export enum ScreenNames {
  WELCOME = 'WELCOME',
  MAIN = 'MAIN',
  ALL_TIPS = 'ALL_TIPS',
  TIP_DETAILS = 'TIP_DETAILS',
}

export enum ThemeModeEnum {
  LIGHT = 'LIGHT',
  DARK = 'DARK',
  SYSTEM = 'SYSTEM',
  NEON = 'NEON',
  SYNTHWAVE = 'SYNTHWAVE',
}

export type ThemeMode = keyof typeof ThemeModeEnum;

export const DEFAULT_OPTIONS: MenuOption[] = [
  {
    id: 'themes',
    label: 'Themes',
    onPress: () => {}, // This will be set in the component
    icon: '🌈',
  },
  {
    id: 'welcome',
    label: 'Welcome',
    onPress: () => console.log('Welcome pressed'),
    icon: '👋',
  },
  {
    id: 'allTips',
    label: 'All Tips',
    onPress: () => console.log('All Tips pressed'),
    icon: '📋',
  },
  {
    id: 'settings',
    label: 'Settings',
    onPress: () => console.log('Settings pressed'),
    icon: '⚙️',
  },
  {
    id: 'profile',
    label: 'Profile',
    onPress: () => console.log('Profile pressed'),
    icon: '👤',
  },
  {
    id: 'about',
    label: 'About',
    onPress: () => console.log('About pressed'),
    icon: 'ℹ️',
  },
];
