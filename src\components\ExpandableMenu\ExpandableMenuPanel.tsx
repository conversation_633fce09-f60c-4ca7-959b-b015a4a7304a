import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import ExpandableMenuOption, {MenuOption} from './ExpandableMenuOption';
import ExpandableMenuCloseIcon from './ExpandableMenuCloseIcon';
import {useTheme} from '../../theme/ThemeContext';
import {useExpandableMenuThemeColors} from './hooks/useExpandableMenuThemeColors';
import Colors from '../../theme/colors';

interface ExpandableMenuPanelProps {
  isOpen: boolean;
  isClosing: boolean;
  slideAnim: Animated.Value;
  options: MenuOption[];
  toggleMenu: () => void;
}

const ExpandableMenuPanel: React.FC<ExpandableMenuPanelProps> = ({
  isOpen,
  isClosing,
  slideAnim,
  options,
  toggleMenu,
}) => {
  const {isNeonMode, isSynthwaveMode} = useTheme();
  const {
    textColor,
    backgroundColor: surfaceColor,
    primaryColor,
    borderColor,
    menuItemSelected: accentColor,
  } = useExpandableMenuThemeColors();

  // Only render when open or closing
  if (!(isOpen || isClosing)) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.menuContainer,
        {
          backgroundColor: surfaceColor,
          borderRightColor: borderColor,
          transform: [{translateX: slideAnim}],
          ...(isNeonMode && {
            shadowColor: Colors.neon.glow.blue,
            shadowOffset: {width: 4, height: 0},
            shadowOpacity: 0.8,
            shadowRadius: 15,
            elevation: 15,
          }),
          ...(isSynthwaveMode && {
            shadowColor: Colors.synthwave.glow.pink,
            shadowOffset: {width: 4, height: 0},
            shadowOpacity: 0.7,
            shadowRadius: 18,
            elevation: 18,
          }),
        },
      ]}>
      <View
        style={[
          styles.menuHeader,
          {
            borderBottomColor: borderColor,
            ...(isNeonMode && {
              borderBottomColor: Colors.neon.border,
              borderBottomWidth: 2,
            }),
            ...(isSynthwaveMode && {
              borderBottomColor: Colors.synthwave.border,
              borderBottomWidth: 2,
            }),
          },
        ]}>
        <View style={styles.menuHeaderContent}>
          <View
            style={[
              styles.menuHeaderAccent,
              {
                backgroundColor: accentColor,
                ...(isNeonMode && {
                  backgroundColor: Colors.neon.accent,
                  shadowColor: Colors.neon.glow.yellow,
                  shadowOffset: {width: 0, height: 0},
                  shadowOpacity: 0.8,
                  shadowRadius: 5,
                  elevation: 5,
                }),
                ...(isSynthwaveMode && {
                  backgroundColor: Colors.synthwave.accent,
                  shadowColor: Colors.synthwave.glow.pink,
                  shadowOffset: {width: 0, height: 0},
                  shadowOpacity: 0.7,
                  shadowRadius: 7,
                  elevation: 7,
                }),
              },
            ]}
          />
          <Text
            style={[
              styles.menuHeaderText,
              {
                color: textColor,
                ...(isNeonMode && {
                  textShadowColor: Colors.neon.glow.blue,
                  textShadowOffset: {width: 0, height: 0},
                  textShadowRadius: 5,
                }),
                ...(isSynthwaveMode && {
                  textShadowColor: Colors.synthwave.glow.pink,
                  textShadowOffset: {width: 0, height: 0},
                  textShadowRadius: 6,
                }),
              },
            ]}>
            Menu
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.closeButton,
            {
              backgroundColor: surfaceColor,
              ...(isNeonMode && {
                borderColor: Colors.neon.border,
                borderWidth: 1,
              }),
              ...(isSynthwaveMode && {
                borderColor: Colors.synthwave.border,
                borderWidth: 1,
              }),
            },
          ]}
          onPress={toggleMenu}
          hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}
          activeOpacity={0.7}>
          <ExpandableMenuCloseIcon iconColor={primaryColor} />
        </TouchableOpacity>
      </View>
      <View style={styles.menuOptions}>
        {options.map((option, index) => (
          <ExpandableMenuOption
            key={option.id}
            option={option}
            isLast={index === options.length - 1}
            onPress={toggleMenu}
          />
        ))}
      </View>
    </Animated.View>
  );
};

const {height} = Dimensions.get('window');

const styles = StyleSheet.create({
  menuContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 280,
    height: height,
    zIndex: 2,
    borderRightWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 4,
      height: 0,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 10,
  },
  menuHeader: {
    padding: 20,
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuHeaderAccent: {
    width: 4,
    height: 24,
    borderRadius: 2,
    marginRight: 12,
  },
  menuHeaderText: {
    fontSize: 22,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: -8,
  },
  menuOptions: {
    paddingTop: 12,
    paddingBottom: 20,
  },
});

export default ExpandableMenuPanel;
