import SQLite, {SQLiteDatabase} from 'react-native-sqlite-storage';
import {Platform} from 'react-native';
import { DATABASE_NAME } from '../models/constants';


export let db: SQLiteDatabase | undefined;

export const initDatabase = () => {
  db = SQLite.openDatabase(
    {
      name: DATABASE_NAME,
      location: 'default',
      createFromLocation: Platform.OS === 'ios' ? 1 : '~www/' + DATABASE_NAME,
    },
    () => {
      console.log('Database opened successfully from pre-packaged file.');
    },
    (error: any) => {
      console.log('Error opening database: ' + error);
    },
  );
};

initDatabase();
