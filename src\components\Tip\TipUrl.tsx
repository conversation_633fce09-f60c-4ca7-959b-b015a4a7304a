import React from 'react';
import {Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useTipThemeColors} from './hooks/useTipThemeColors';

interface TipUrlProps {
  url?: string; // Make url optional
  onPress: () => void;
}

const TipUrl: React.FC<TipUrlProps> = ({url, onPress}) => {
  const {
    primaryColor,
    surfaceColor,
    borderColor,
    textSecondaryColor,
    menuGlowColor,
  } = useTipThemeColors();

  // Render nothing if url is not provided
  if (!url) {
    return null;
  }

  // Format URL for display
  const displayUrl = url.length > 35 ? url.substring(0, 32) + '...' : url;

  return (
    <TouchableOpacity
      style={[
        styles.linkContainer,
        {
          backgroundColor: surfaceColor,
          borderColor: borderColor,
          borderWidth: 1,
          ...(menuGlowColor && {
            shadowColor: menuGlowColor,
            shadowOffset: {width: 0, height: 0},
            shadowOpacity: 0.5,
            shadowRadius: 5,
            elevation: 5,
          }),
        },
      ]}
      onPress={() => onPress()}
      activeOpacity={0.7}>
      <Text style={[styles.linkText, {color: primaryColor}]}>Visit Resource</Text>
      <Text
        style={[styles.urlText, {color: textSecondaryColor}]}
        numberOfLines={1}>
        {displayUrl}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  linkContainer: {
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    marginBottom: 4,
    alignItems: 'center',
  },
  linkText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  urlText: {
    fontSize: 14,
    color: '#888',
    opacity: 0.8,
  },
});

export default TipUrl;
