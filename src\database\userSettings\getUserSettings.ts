import {SQLiteDatabase} from 'react-native-sqlite-storage';
import {UserSettings} from '../../models/types';

export function getUserSettings(db: SQLiteDatabase): Promise<UserSettings[]> {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        'SELECT * FROM UserSettings',
        [],
        (_, results) => {
          const rows = results.rows.raw();
          resolve(rows as UserSettings[]);
        },
        (_, error) => {
          reject(error);
          return false;
        },
      );
    });
  });
}
