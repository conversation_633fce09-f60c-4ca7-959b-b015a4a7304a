import React from 'react';
import {TouchableOpacity, Text, StyleSheet, Dimensions} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useNavigation} from '../../../App';
import {ScreenNames} from '../../models/constants';
import {useWelcomeScreenThemeColors} from './hooks/useWelcomeScreenThemeColors';

const {width} = Dimensions.get('window');

interface GetStartedProps {}

const GetStarted: React.FC<GetStartedProps> = () => {
  const {navigateTo} = useNavigation();
  const colors = useWelcomeScreenThemeColors();

  const handleGetStarted = () => {
    navigateTo(ScreenNames.MAIN);
  };

  if (colors.gradientColors) {
    return (
      <LinearGradient
        colors={colors.gradientColors}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        style={styles.gradientButton}>
        <TouchableOpacity
          style={styles.getStartedButtonInner}
          onPress={handleGetStarted}
          activeOpacity={0.8}>
          <Text
            style={[
              styles.getStartedButtonText,
              {color: colors.backgroundColor},
            ]}>
            Get Started
          </Text>
        </TouchableOpacity>
      </LinearGradient>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.getStartedButton, {backgroundColor: colors.primaryColor}]}
      onPress={handleGetStarted}
      activeOpacity={0.8}>
      <Text style={[styles.getStartedButtonText, {color: '#FFFFFF'}]}>
        Get Started
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  getStartedButton: {
    paddingVertical: 16,
    paddingHorizontal: 48,
    borderRadius: 25,
    minWidth: width * 0.6,
    alignItems: 'center',
  },
  gradientButton: {
    borderRadius: 25,
  },
  getStartedButtonInner: {
    paddingVertical: 16,
    paddingHorizontal: 48,
    alignItems: 'center',
    width: '100%',
  },
  getStartedButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
});

export default GetStarted;
