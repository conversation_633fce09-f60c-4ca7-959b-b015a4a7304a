export interface Tip {
  id: number;
  title: string;
  subtitle: string; // Renamed from body
  url: string;
  img: string;
}

export interface Detail {
  id: string; // UUID
  url: string;
  summary: string;
  graph: string; // Assuming graph data is a string for now
}

export interface TipWithDetails extends Tip {
  details: Detail | null; // A tip might not have details
}

export interface UserSettings {
  id: number;
  fontSize: string | null;
  showWelcomeScreen: number | null;
  theme: string | null;
}
