import {useTheme} from '../../../theme/ThemeContext';
import Colors from '../../../theme/colors';

export function useWelcomeScreenThemeColors() {
  const {isDarkMode, isNeonMode, isSynthwaveMode} = useTheme();

  if (isSynthwaveMode) {
    return {
      // Text colors
      textColor: Colors.synthwave.text,
      textSecondaryColor: Colors.synthwave.textSecondary,
      
      // Background colors
      cardBgColor: Colors.synthwave.cardBg,
      
      // Border colors
      borderColor: Colors.synthwave.border,
      
      // Primary and accent colors
      primaryColor: Colors.synthwave.primary,
      accentColor: Colors.synthwave.accent,
      
      // Glow effects
      iconGlowColor: Colors.synthwave.glow.purple,
      textGlowColor: Colors.synthwave.glow.purple,
      
      // Shadow properties
      shadowOpacity: 0.4,
      shadowRadius: 10,
      elevation: 10,
      textShadowRadius: 5,
      textShadowOffset: {width: 0, height: 0},
      
      // Background colors
      backgroundColor: Colors.synthwave.background,
    };
  } else if (isNeonMode) {
    return {
      // Text colors
      textColor: Colors.neon.text,
      textSecondaryColor: Colors.neon.textSecondary,
      
      // Background colors
      cardBgColor: Colors.neon.cardBg,
      
      // Border colors
      borderColor: Colors.neon.border,
      
      // Primary and accent colors
      primaryColor: Colors.neon.primary,
      accentColor: Colors.neon.secondary,
      
      // Glow effects
      iconGlowColor: Colors.neon.glow.cyan,
      textGlowColor: Colors.neon.glow.magenta,
      
      // Shadow properties
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
      textShadowRadius: 5,
      textShadowOffset: {width: 0, height: 0},
      
      // Background colors
      backgroundColor: Colors.neon.background,
      
      // Gradient colors
      gradientColors: [Colors.neon.glow.magenta, Colors.neon.glow.cyan],
    };
  } else {
    return {
      // Text colors
      textColor: isDarkMode ? Colors.dark.text : Colors.light.text,
      textSecondaryColor: isDarkMode ? Colors.dark.textSecondary : Colors.light.textSecondary,
      
      // Background colors
      cardBgColor: isDarkMode ? Colors.dark.cardBg : Colors.light.cardBg,
      
      // Border colors
      borderColor: isDarkMode ? Colors.dark.border : Colors.light.border,
      
      // Primary and accent colors
      primaryColor: isDarkMode ? Colors.dark.primary : Colors.light.primary,
      accentColor: isDarkMode ? Colors.dark.accent : Colors.light.accent,
      
      // Glow effects
      iconGlowColor: undefined,
      textGlowColor: undefined,
      
      // Shadow properties
      shadowOpacity: undefined,
      shadowRadius: undefined,
      elevation: undefined,
      textShadowRadius: undefined,
      textShadowOffset: undefined,
      
      // Background colors
      backgroundColor: isDarkMode ? Colors.dark.background : Colors.light.background,
      
      // Gradient colors
      gradientColors: undefined,
    };
  }
} 