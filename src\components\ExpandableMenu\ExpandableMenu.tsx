import React, {useState} from 'react';
import {TouchableOpacity, StyleSheet, Animated} from 'react-native';
import Colors from '../../theme/colors';
import {useTheme} from '../../theme/ThemeContext';
import {useExpandableMenuThemeColors} from './hooks/useExpandableMenuThemeColors';
import {MenuOption} from './ExpandableMenuOption';
import ExpandableMenuIcon from './ExpandableMenuIcon';
import ExpandableMenuPanel from './ExpandableMenuPanel';
import ThemeModal from '../ThemeModal/ThemeModal';
import {useNavigation} from '../../../App';
import {DEFAULT_OPTIONS, ScreenNames} from '../../models/constants';

// Define component props
interface ExpandableMenuProps {
  options?: MenuOption[];
}

const ExpandableMenu: React.FC<ExpandableMenuProps> = ({
  options: propOptions,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [isThemeModalVisible, setIsThemeModalVisible] = useState(false);
  // Initialize animation values
  const slideAnim = useState(() => new Animated.Value(-250))[0];
  const fadeAnim = useState(() => new Animated.Value(0))[0];
  const {isNeonMode, isSynthwaveMode} = useTheme();
  const {borderColor, primaryColor} = useExpandableMenuThemeColors();
  const {navigateTo} = useNavigation();

  // Create a copy of the options with the theme toggle function and navigation
  const options = useState(() => {
    const updatedOptions = propOptions || [...DEFAULT_OPTIONS];

    // Find and update the themes option
    const themesOption = updatedOptions.find(option => option.id === 'themes');
    if (themesOption) {
      themesOption.onPress = () => setIsThemeModalVisible(true);
    }

    // Find and update the welcome option
    const welcomeOption = updatedOptions.find(
      option => option.id === 'welcome',
    );
    if (welcomeOption) {
      welcomeOption.onPress = () => navigateTo(ScreenNames.WELCOME);
    }

    // Find and update the allTips option
    const allTipsOption = updatedOptions.find(
      option => option.id === 'allTips',
    );
    if (allTipsOption) {
      allTipsOption.onPress = () => navigateTo(ScreenNames.ALL_TIPS);
    }

    return updatedOptions;
  })[0];

  // Colors are now provided by useThemeColors

  const toggleMenu = () => {
    if (isOpen) {
      // Start closing animation
      setIsClosing(true);

      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -250,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Animation completed
        setIsOpen(false);
        setIsClosing(false);
      });
    } else {
      // Open the menu
      setIsOpen(true);

      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const renderMenuIcon = () => {
    return <ExpandableMenuIcon iconColor="#FFFFFF" />;
  };

  return (
    <>
      {/* Menu Button */}
      <TouchableOpacity
        style={[
          styles.menuButton,
          {
            backgroundColor: primaryColor,
            borderColor: borderColor,
            ...(isNeonMode && {
              shadowColor: Colors.neon.glow.magenta,
              shadowOffset: {width: 0, height: 0},
              shadowOpacity: 0.8,
              shadowRadius: 10,
              elevation: 10,
            }),
            ...(isSynthwaveMode && {
              shadowColor: Colors.synthwave.glow.pink,
              shadowOffset: {width: 0, height: 0},
              shadowOpacity: 0.7,
              shadowRadius: 12,
              elevation: 12,
            }),
          },
        ]}
        onPress={toggleMenu}
        activeOpacity={0.7}>
        {renderMenuIcon()}
      </TouchableOpacity>

      {/* Overlay */}
      {isOpen && (
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={toggleMenu}>
          <Animated.View
            style={[
              styles.overlayBackground,
              {
                opacity: fadeAnim,
              },
            ]}
          />
        </TouchableOpacity>
      )}

      {/* Menu Panel */}
      <ExpandableMenuPanel
        isOpen={isOpen}
        isClosing={isClosing}
        slideAnim={slideAnim}
        options={options}
        toggleMenu={toggleMenu}
      />

      {/* Theme Modal */}
      <ThemeModal
        visible={isThemeModalVisible}
        onClose={() => setIsThemeModalVisible(false)}
      />
    </>
  );
};

const styles = StyleSheet.create({
  menuButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  overlayBackground: {
    flex: 1,
    backgroundColor: 'black',
  },
});

export default ExpandableMenu;
