import {ResultSet, Transaction} from 'react-native-sqlite-storage';
import {db} from '../database';
import {TipWithDetails, Detail} from '../../models/types';

// Helper function to map raw DB result to TipWithDetails
const mapRowToTipWithDetails = (row: any): TipWithDetails | null => {
  if (!row) {
    return null;
  }
  const details: Detail | null = row.detail_id
    ? {
        id: row.detail_id,
        url: row.detailUrl,
        summary: row.summary,
        graph: row.graph,
      }
    : null;

  return {
    id: row.id,
    title: row.title,
    subtitle: row.subtitle,
    url: row.url,
    img: row.img || '',
    details: details,
  };
};

/**
 * Fetches a specific tip by its ID from the database
 * @param id The ID of the tip to fetch
 * @returns A Promise that resolves with the tip if found, or null if not found
 * @throws Will throw an error if there's a database error
 */
export const getTipById = (id: number): Promise<TipWithDetails | null> => {
  return new Promise((resolve, reject) => {
    if (!db) {
      const error = new Error('Database not initialized');
      console.error(error);
      reject(error);
      return;
    }

    db.transaction((tx: Transaction) => {
      // Use LEFT JOIN to include tips even if they don't have details
      const query = `
        SELECT
          Tips.*,
          Details.id AS detail_id, Details.summary, Details.graph, Details.url AS detailUrl
        FROM Tips
        LEFT JOIN Details ON Tips.detail_id = Details.id
        WHERE Tips.id = ?
        LIMIT 1;
      `;


      tx.executeSql(
        query,
        [id],
        (_: Transaction, results: ResultSet) => {
          if (results.rows.length > 0) {
            const rawTip = results.rows.item(0);
            const tip = mapRowToTipWithDetails(rawTip);
            console.log(`Retrieved tip with ID ${id} from database`);
            resolve(tip);
          } else {
            console.log(`No tip found with ID ${id}`);
            resolve(null);
          }
        },
        (_: Transaction, error: any) => {
          const errorMsg = `Error fetching tip with ID ${id}: ${error.message || error}`;
          console.error(errorMsg);
          reject(new Error(errorMsg));
          return false; // Return false to signal the error was handled
        },
      );
    }, (error) => {
      const errorMsg = `Transaction error: ${error.message || error}`;
      console.error(errorMsg);
      reject(new Error(errorMsg));
    });
  });
};
