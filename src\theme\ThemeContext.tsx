import React, {createContext, useState, useContext, useEffect} from 'react';
import {useColorScheme, Appearance} from 'react-native';
import {db} from '../database/database';
import {getUserSettings} from '../database/userSettings/getUserSettings';
import {updateUserTheme} from '../database/userSettings/updateUserTheme';
import {ThemeMode, ThemeModeEnum} from '../models/constants';

interface ThemeContextType {
  themeMode: ThemeMode;
  isDarkMode: boolean;
  isNeonMode: boolean;
  isSynthwaveMode: boolean;
  setThemeMode: (mode: ThemeMode) => void;
}

const ThemeContext = createContext<ThemeContextType>({
  themeMode: ThemeModeEnum.SYSTEM.toUpperCase() as ThemeMode,
  isDarkMode: false,
  isNeonMode: false,
  isSynthwaveMode: false,
  setThemeMode: () => {},
});

export const ThemeProvider: React.FC<{children: React.ReactNode}> = ({
  children,
}) => {
  // Get the device color scheme
  const deviceColorScheme = useColorScheme();

  // Initialize theme mode from storage or default to system
  const [themeMode, setThemeMode] = useState<ThemeMode>(
    ThemeModeEnum.SYSTEM.toUpperCase() as ThemeMode,
  );
  const [userSettingsId, setUserSettingsId] = useState<number | null>(null);

  // Load theme from database on mount
  useEffect(() => {
    if (db) {
      getUserSettings(db)
        .then(settings => {
          if (settings.length > 0) {
            const userSettings = settings[0]; // Get first user settings entry
            setUserSettingsId(userSettings.id);
            if (userSettings.theme) {
              setThemeMode(userSettings.theme as ThemeMode);
            }
          }
        })
        .catch(error => {
          console.error('Failed to load theme from database:', error);
        });
    }
  }, []);

  // Save theme to database when it changes
  const handleThemeChange = (mode: ThemeMode) => {
    setThemeMode(mode);
    if (db && userSettingsId) {
      updateUserTheme(db, userSettingsId, mode)
        .then(() => {
          console.log('Theme updated in database');
        })
        .catch(error => {
          console.error('Failed to update theme in database:', error);
        });
    }
  };

  // Determine if dark mode is active based on theme mode and device settings
  const isDarkMode =
    themeMode === ThemeModeEnum.SYSTEM.toUpperCase()
      ? deviceColorScheme === ThemeModeEnum.DARK.toLowerCase()
      : themeMode === ThemeModeEnum.DARK.toUpperCase() ||
        themeMode === ThemeModeEnum.SYNTHWAVE.toUpperCase();

  // Determine if neon mode is active
  const isNeonMode = themeMode === ThemeModeEnum.NEON.toUpperCase();

  // Determine if synthwave mode is active
  const isSynthwaveMode = themeMode === ThemeModeEnum.SYNTHWAVE.toUpperCase();

  // Listen for changes in device color scheme
  useEffect(() => {
    const subscription = Appearance.addChangeListener(_preferences => {
      // Only update if we're using system theme
      if (themeMode === ThemeModeEnum.SYSTEM.toUpperCase()) {
        // No need to update state as the isDarkMode calculation will handle this
      }
    });

    return () => {
      subscription.remove();
    };
  }, [themeMode]);

  // Provide the theme context
  const contextValue: ThemeContextType = {
    themeMode,
    isDarkMode,
    isNeonMode,
    isSynthwaveMode,
    setThemeMode: handleThemeChange,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

export default ThemeContext;
