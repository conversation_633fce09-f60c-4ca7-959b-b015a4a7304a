import React, {useState, createContext, useContext} from 'react';
import {StatusBar, View, ViewStyle} from 'react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import MainScreen from './src/screens/MainScreen';
import AllTipsScreen from './src/screens/AllTipsScreen';
import WelcomeScreen from './src/screens/WelcomeScreen';
import {ThemeProvider, useTheme} from './src/theme/ThemeContext';
import AppColors from './src/theme/colors';
import { ScreenNames } from './src/models/constants';



// Create navigation context
interface NavigationContextType {
  currentScreen: ScreenNames;
  currentTipId: number | null;
  navigateTo: (screen: ScreenNames, tipId?: number) => void;
}

const NavigationContext = createContext<NavigationContextType>({
  currentScreen: ScreenNames.WELCOME,
  currentTipId: null,
  navigateTo: () => {},
});

export const useNavigation = () => useContext(NavigationContext);

// Main app content with theme context
const AppContent = () => {
  const {isDarkMode, isNeonMode} = useTheme();
  const [currentScreen, setCurrentScreen] = useState<ScreenNames>(
    ScreenNames.WELCOME,
  );
  const [currentTipId, setCurrentTipId] = useState<number | null>(null);

  // Navigation function
  const navigateTo = (screen: ScreenNames, tipId?: number) => {
    setCurrentScreen(screen);
    if (tipId !== undefined) {
      setCurrentTipId(tipId);
    } else if (screen !== ScreenNames.TIP_DETAILS) {
      // Reset the tip ID when navigating to screens other than TIP_DETAILS
      setCurrentTipId(null);
    }
  };

  // Get the appropriate background color based on the theme
  let backgroundColor;
  let statusBarStyle: 'light-content' | 'dark-content';

  if (isNeonMode) {
    backgroundColor = AppColors.neon.background;
    statusBarStyle = 'light-content';
  } else {
    backgroundColor = isDarkMode
      ? AppColors.dark.background
      : AppColors.light.background;
    statusBarStyle = isDarkMode ? 'light-content' : 'dark-content';
  }

  const backgroundStyle: ViewStyle = {
    backgroundColor,
  };

  // Render the current screen
  const renderScreen = () => {
    switch (currentScreen) {
      case ScreenNames.WELCOME:
        return <WelcomeScreen />;
      case ScreenNames.MAIN:
        return <MainScreen />;
      case ScreenNames.ALL_TIPS:
        return <AllTipsScreen />;
      case ScreenNames.TIP_DETAILS:
        // Pass the currentTipId to MainScreen when in TIP_DETAILS mode
        return <MainScreen initialTipId={currentTipId} />;
      default:
        return <WelcomeScreen />;
    }
  };

  return (
    <NavigationContext.Provider
      value={{currentScreen, currentTipId, navigateTo}}>
      <View style={[backgroundStyle, {flex: 1} as ViewStyle]}>
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={backgroundColor}
        />
        {renderScreen()}
      </View>
    </NavigationContext.Provider>
  );
};

function App(): React.JSX.Element {
  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

export default App;
