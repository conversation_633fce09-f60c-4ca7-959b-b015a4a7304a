import {useTheme} from '../../../theme/ThemeContext';
import Colors from '../../../theme/colors';

export function useTipListThemeColors() {
  const {isDarkMode, isNeonMode, isSynthwaveMode} = useTheme();

  if (isSynthwaveMode) {
    return {
      // Text colors
      textColor: Colors.synthwave.glow.coral,
      textSecondaryColor: Colors.synthwave.textSecondary,
      headerTextColor: Colors.synthwave.glow.pink,
      backButtonTextColor: Colors.synthwave.glow.blue,

      // Background colors
      backgroundColor: Colors.synthwave.surface,
      cardBackgroundColor: Colors.synthwave.cardBg,
      gradientColors: ['#1a1a2e', '#241b2f'],

      // Border colors
      borderColor: Colors.synthwave.border,
      cardBorderColor: Colors.synthwave.success,

      // Accent and primary colors
      primaryColor: Colors.synthwave.primary,
      accentColor: Colors.synthwave.accent,

      // Shadow and glow effects
      cardShadow: {
        shadowColor: Colors.synthwave.glow.pink,
        shadowOffset: {width: 0, height: 0},
        shadowOpacity: 0.8,
        shadowRadius: 15,
        elevation: 15,
      },

      // Text shadow effects
      titleTextShadow: {
        textShadowColor: Colors.synthwave.glow.blue,
        textShadowOffset: {width: 0, height: 0},
        textShadowRadius: 0,
      },
      subtitleTextShadow: {
        textShadowColor: Colors.synthwave.glow.purple,
        textShadowOffset: {width: 0, height: 0},
        textShadowRadius: 4,
      },
      backButtonTextShadow: {
        textShadowColor: Colors.synthwave.glow.pink,
        textShadowOffset: {width: 0, height: 0},
        textShadowRadius: 8,
      },

      // Image placeholder gradient
      imagePlaceholderGradient: {
        colors: [Colors.synthwave.glow.pink, Colors.synthwave.glow.blue],
        start: {x: 0, y: 0},
        end: {x: 1, y: 1},
      },

      // Border widths
      cardBorderWidth: 1.5,
    };
  } else if (isNeonMode) {
    return {
      // Text colors
      textColor: Colors.neon.text,
      textSecondaryColor: Colors.neon.textSecondary,
      headerTextColor: Colors.neon.glow.magenta,
      backButtonTextColor: Colors.neon.glow.cyan,

      // Background colors
      backgroundColor: Colors.neon.surface,
      cardBackgroundColor: Colors.neon.cardBg,
      gradientColors: ['#151528', '#1A1A36'],

      // Border colors
      borderColor: Colors.neon.border,
      cardBorderColor: Colors.neon.glow.blue,

      // Accent and primary colors
      primaryColor: Colors.neon.primary,
      accentColor: Colors.neon.accent,

      // Shadow and glow effects
      cardShadow: {
        shadowColor: Colors.neon.glow.blue,
        shadowOffset: {width: 0, height: 0},
        shadowOpacity: 0.8,
        shadowRadius: 10,
        elevation: 10,
      },

      // Text shadow effects
      titleTextShadow: {
        textShadowColor: Colors.neon.glow.blue,
        textShadowOffset: {width: 0, height: 0},
        textShadowRadius: 5,
      },
      subtitleTextShadow: {
        textShadowColor: Colors.neon.glow.blue,
        textShadowOffset: {width: 0, height: 0},
        textShadowRadius: 2,
      },
      backButtonTextShadow: {
        textShadowColor: Colors.neon.glow.blue,
        textShadowOffset: {width: 0, height: 0},
        textShadowRadius: 5,
      },

      // Image placeholder gradient
      imagePlaceholderGradient: {
        colors: [Colors.neon.glow.magenta, Colors.neon.glow.blue],
        start: {x: 0, y: 0},
        end: {x: 1, y: 1},
      },

      // Border widths
      cardBorderWidth: 1.5,
    };
  } else {
    return {
      // Text colors
      textColor: isDarkMode ? Colors.dark.text : Colors.light.text,
      textSecondaryColor: isDarkMode
        ? Colors.dark.textSecondary
        : Colors.light.textSecondary,
      headerTextColor: isDarkMode ? Colors.dark.text : Colors.light.text,
      backButtonTextColor: isDarkMode ? Colors.dark.text : Colors.light.text,

      // Background colors
      backgroundColor: isDarkMode ? Colors.dark.surface : Colors.light.surface,
      cardBackgroundColor: isDarkMode
        ? Colors.dark.cardBg
        : Colors.light.cardBg,
      gradientColors: undefined,

      // Border colors
      borderColor: isDarkMode ? Colors.dark.border : Colors.light.border,
      cardBorderColor: isDarkMode ? Colors.dark.border : Colors.light.border,

      // Accent and primary colors
      primaryColor: isDarkMode ? Colors.dark.primary : Colors.light.primary,
      accentColor: isDarkMode ? Colors.dark.accent : Colors.light.accent,

      // Shadow and glow effects
      cardShadow: isDarkMode
        ? {
            shadowColor: '#000',
            shadowOffset: {width: 0, height: 2},
            shadowOpacity: 0.3,
            shadowRadius: 4,
            elevation: 5,
          }
        : {
            shadowColor: '#000',
            shadowOffset: {width: 0, height: 1},
            shadowOpacity: 0.2,
            shadowRadius: 3,
            elevation: 3,
          },

      // Text shadow effects
      titleTextShadow: undefined,
      subtitleTextShadow: undefined,
      backButtonTextShadow: undefined,

      // Image placeholder gradient
      imagePlaceholderGradient: undefined,

      // Border widths
      cardBorderWidth: 1,
    };
  }
}
