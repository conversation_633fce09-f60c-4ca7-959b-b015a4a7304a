import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useExpandableMenuThemeColors} from './hooks/useExpandableMenuThemeColors';

// Define menu option interface
export interface MenuOption {
  id: string;
  label: string;
  onPress: () => void;
  icon?: string; // Optional icon name
}

// Define component props
interface ExpandableMenuOptionProps {
  option: MenuOption;
  isLast: boolean;
  onPress: () => void;
}

const ExpandableMenuOption: React.FC<ExpandableMenuOptionProps> = ({
  option,
  isLast,
  onPress,
}) => {
  const {
    textColor,
    menuItemBackground: surfaceColor,
    primaryColor,
    borderColor,
    menuGlowColor,
    menuIndicatorShadowColor,
    menuIndicatorShadowOpacity,
    menuIndicatorShadowRadius,
    menuIndicatorElevation,
    menuTextShadowColor,
    menuTextShadowRadius,
  } = useExpandableMenuThemeColors();

  const handlePress = () => {
    option.onPress();
    onPress();
  };

  return (
    <React.Fragment>
      <TouchableOpacity
        style={[
          styles.menuOption,
          {
            backgroundColor: surfaceColor,
            ...(menuGlowColor
              ? {borderLeftWidth: 2, borderLeftColor: menuGlowColor}
              : {}),
          },
        ]}
        onPress={handlePress}
        activeOpacity={0.7}>
        <View style={styles.menuOptionContent}>
          {option.icon && (
            <Text style={styles.menuOptionIcon}>{option.icon}</Text>
          )}
          <Text
            style={[
              styles.menuOptionText,
              {
                color: textColor,
                textShadowColor:
                  option.id === 'themes' ? menuTextShadowColor : undefined,
                textShadowOffset:
                  option.id === 'themes' && menuTextShadowColor
                    ? {width: 0, height: 0}
                    : undefined,
                textShadowRadius:
                  option.id === 'themes' ? menuTextShadowRadius : undefined,
              },
            ]}>
            {option.label}
          </Text>
        </View>
        <View
          style={[
            styles.menuOptionIndicator,
            {
              backgroundColor: primaryColor,
              shadowColor: menuIndicatorShadowColor,
              shadowOffset: menuIndicatorShadowColor
                ? {width: 0, height: 0}
                : undefined,
              shadowOpacity: menuIndicatorShadowOpacity,
              shadowRadius: menuIndicatorShadowRadius,
              elevation: menuIndicatorElevation,
            },
          ]}
        />
      </TouchableOpacity>
      {!isLast && (
        <View style={[styles.menuDivider, {backgroundColor: borderColor}]} />
      )}
    </React.Fragment>
  );
};

const styles = StyleSheet.create({
  menuOption: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuOptionIcon: {
    fontSize: 20,
    marginRight: 16,
  },
  menuOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  menuOptionIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    opacity: 0.7,
  },
  menuDivider: {
    height: 1,
    marginHorizontal: 20,
    opacity: 0.5,
  },
});

export default ExpandableMenuOption;
