import React from 'react';
import {Text, StyleSheet} from 'react-native';
import {useTipListThemeColors} from './hooks/useTipListThemeColors';

interface TipListItemTitleProps {
  title: string;
}

const TipListItemTitle: React.FC<TipListItemTitleProps> = ({title}) => {
  const colors = useTipListThemeColors();

  return (
    <Text
      style={[
        styles.tipTitle,
        {
          color: colors.textColor,
          ...colors.titleTextShadow,
        },
      ]}
      numberOfLines={2}>
      {title}
    </Text>
  );
};

const styles = StyleSheet.create({
  tipTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    letterSpacing: 0.3,
  },
});

export default TipListItemTitle;
