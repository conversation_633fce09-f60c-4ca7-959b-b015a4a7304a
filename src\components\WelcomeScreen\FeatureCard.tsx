import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useWelcomeScreenThemeColors} from './hooks/useWelcomeScreenThemeColors';

interface FeatureCardProps {
  icon: string;
  title: string;
  description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  icon,
  title,
  description,
}) => {
  const colors = useWelcomeScreenThemeColors();

  return (
    <View
      style={[
        styles.featureCard,
        {
          backgroundColor: colors.cardBgColor,
          borderColor: colors.borderColor,
        },
        (colors.shadowOpacity !== undefined) && {
          shadowColor: colors.iconGlowColor,
          shadowOffset: {width: 0, height: 0},
          shadowOpacity: colors.shadowOpacity,
          shadowRadius: colors.shadowRadius,
          elevation: colors.elevation,
        },
      ]}>
      <Text
        style={[
          styles.featureIcon,
          (colors.textShadowRadius !== undefined) && {
            color: colors.accentColor,
            textShadowColor: colors.iconGlowColor,
            textShadowOffset: colors.textShadowOffset,
            textShadowRadius: colors.textShadowRadius,
          },
        ]}>
        {icon}
      </Text>
      <Text
        style={[
          styles.featureTitle,
          {color: colors.textColor},
          (colors.textShadowRadius !== undefined) && {
            textShadowColor: colors.textGlowColor,
            textShadowOffset: colors.textShadowOffset,
            textShadowRadius: colors.textShadowRadius,
          },
        ]}>
        {title}
      </Text>
      <Text style={[styles.featureDescription, {color: colors.textSecondaryColor}]}>
        {description}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  featureCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  featureIcon: {
    fontSize: 32,
    textAlign: 'center',
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});

export default FeatureCard;
