import React from 'react';
import {Text, StyleSheet, View} from 'react-native';
import {useTipThemeColors} from './hooks/useTipThemeColors';

interface TipSummaryProps {
  summary?: string; // Make summary optional
}

const TipSummary: React.FC<TipSummaryProps> = ({summary}) => {
  const {textSecondaryColor, secondaryColor} = useTipThemeColors();

  // Render nothing if summary is not provided
  if (!summary) {
    return null;
  }

  // Function to format and render summary paragraphs
  const renderFormattedSummary = (text: string) => {
    return text
      .split(/\n{1,2}/)
      .filter(para => para.trim() !== '')
      .map((paragraph, index) => (
        <Text
          key={index}
          style={[
            styles.summaryText,
            {color: textSecondaryColor},
            index > 0 && styles.paragraphSpacing,
          ]}>
          {paragraph}
        </Text>
      ));
  };

  return (
    <View
      style={[
        styles.summaryContainer,
        {
          borderLeftWidth: 2,
          borderLeftColor: secondaryColor,
          paddingLeft: 8,
        },
      ]}>
      <Text
        style={[
          styles.summaryLabel,
          {
            color: secondaryColor,
          },
        ]}>
        Summary
      </Text>
      {renderFormattedSummary(summary)}
    </View>
  );
};

const styles = StyleSheet.create({
  summaryContainer: {
    marginBottom: 16,
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  summaryText: {
    fontSize: 15,
    fontStyle: 'italic',
    lineHeight: 22,
  },
  paragraphSpacing: {
    marginTop: 12,
  },
});

export default TipSummary;
