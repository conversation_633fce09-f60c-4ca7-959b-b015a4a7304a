import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '../theme/ThemeContext';
import Colors from '../theme/colors';
import FeatureCard from '../components/WelcomeScreen/FeatureCard';
import GetStarted from '../components/WelcomeScreen/GetStarted';
import WelcomeScreenHeader from '../components/WelcomeScreen/WelcomeScreenHeader';
import WelcomeScreenDescription from '../components/WelcomeScreen/WelcomeScreenDescription';
import {getUserSettings} from '../database/userSettings/getUserSettings';
import {db} from '../database/database';
import {UserSettings} from '../models/types';

const WelcomeScreen = (): React.JSX.Element => {
  const {isDarkMode, isNeonMode, isSynthwaveMode} = useTheme();
  const insets = useSafeAreaInsets();
  const [_userSettings, setUserSettings] = useState<UserSettings[] | null>(
    null,
  );

  useEffect(() => {
    if (db) {
      getUserSettings(db)
        .then(settings => {
          setUserSettings(settings);
          // You can now use the settings, e.g., to influence the theme or other component behavior
          console.log('User settings:', settings);
        })
        .catch(error => {
          console.error('Failed to get user settings:', error);
        });
    }
  }, []);

  // Get colors based on the active theme
  let backgroundColor: string,
    textColor: string,
    textSecondaryColor: string,
    primaryColor: string,
    sectionTitleStyle: any = {};

  if (isSynthwaveMode) {
    backgroundColor = Colors.synthwave.background;
    textColor = Colors.synthwave.text;
    textSecondaryColor = Colors.synthwave.textSecondary;
    primaryColor = Colors.synthwave.primary;
    sectionTitleStyle = {
      color: Colors.synthwave.primary,
    };
  } else if (isNeonMode) {
    backgroundColor = Colors.neon.background;
    textColor = Colors.neon.text;
    textSecondaryColor = Colors.neon.textSecondary;
    primaryColor = Colors.neon.primary;
    sectionTitleStyle = {
      color: Colors.neon.secondary,
      textShadowColor: Colors.neon.secondary,
      textShadowOffset: {width: 0, height: 0},
      textShadowRadius: 3,
    };
  } else {
    backgroundColor = isDarkMode
      ? Colors.dark.background
      : Colors.light.background;
    textColor = isDarkMode ? Colors.dark.text : Colors.light.text;
    textSecondaryColor = isDarkMode
      ? Colors.dark.textSecondary
      : Colors.light.textSecondary;
    primaryColor = isDarkMode ? Colors.dark.primary : Colors.light.primary;
  }

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor}]}
      edges={['left', 'right', 'top']}>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={[
          styles.scrollContent,
          {paddingBottom: Math.max(20, insets.bottom)},
        ]}
        showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <WelcomeScreenHeader
          isNeonMode={isNeonMode}
          isSynthwaveMode={isSynthwaveMode}
          isDarkMode={isDarkMode}
          textColor={textColor}
          textSecondaryColor={textSecondaryColor}
          primaryColor={primaryColor}
        />

        {/* Description Section */}
        <WelcomeScreenDescription
          textSecondaryColor={textSecondaryColor}
          isSynthwaveMode={isSynthwaveMode}
        />

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <Text
            style={[
              styles.sectionTitle,
              {color: textColor},
              sectionTitleStyle,
            ]}>
            What You'll Get
          </Text>

          <FeatureCard
            icon="📚"
            title="Daily Tips"
            description="Receive carefully curated daily tips covering all aspects of pregnancy health and wellness."
            isSynthwaveMode={isSynthwaveMode}
          />
          <FeatureCard
            icon="🍎"
            title="Nutrition Guidance"
            description="Learn about proper nutrition, essential vitamins, and foods to avoid during pregnancy."
            isSynthwaveMode={isSynthwaveMode}
          />
          <FeatureCard
            icon="🏃‍♀️"
            title="Exercise & Wellness"
            description="Discover safe exercises and wellness practices to keep you and your baby healthy."
            isSynthwaveMode={isSynthwaveMode}
          />
          <FeatureCard
            icon="👩‍⚕️"
            title="Medical Insights"
            description="Get expert advice on prenatal care, check-ups, and important health considerations."
            isSynthwaveMode={isSynthwaveMode}
          />
        </View>

        {/* Get Started Button */}
        <View style={styles.buttonSection}>
          <GetStarted
            isNeonMode={isNeonMode}
            isSynthwaveMode={isSynthwaveMode}
            primaryColor={primaryColor}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  featuresSection: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  buttonSection: {
    paddingTop: 20,
    alignItems: 'center',
  },
});

export default WelcomeScreen;
