import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';

import {useTheme} from '../../theme/ThemeContext';
import {useThemeModalColors} from './hooks/useThemeModalColors';
import {ThemeMode} from '../../models/constants';

interface ThemeModalOptionProps {
  mode: ThemeMode;
  label: string;
  backgroundColor: string;
  textColor: string;
  colorSwatches: string[];
  onSelect: (mode: ThemeMode) => void;
}

const ThemeModalOption: React.FC<ThemeModalOptionProps> = ({
  mode,
  label,
  backgroundColor,
  textColor,
  colorSwatches,
  onSelect,
}) => {
  const {themeMode} = useTheme();
  const {modalBorderColor, optionSelectedColor} = useThemeModalColors();

  return (
    <TouchableOpacity
      style={[
        styles.themeButton,
        {
          backgroundColor: backgroundColor,
          borderColor: themeMode === mode ? optionSelectedColor : modalBorderColor,
          borderWidth: themeMode === mode ? 2 : 1,
        },
      ]}
      onPress={() => onSelect(mode)}>
      <View style={styles.themeButtonContent}>
        <Text style={[styles.themeButtonText, {color: textColor}]}>
          {label}
        </Text>
        <View style={styles.themeColorIndicator}>
          {colorSwatches.map((color, index) => (
            <View
              key={`${mode}-swatch-${index}`}
              style={[styles.colorSwatch, {backgroundColor: color}]}
            />
          ))}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  themeButton: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    width: '100%',
  },
  themeButtonContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  themeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  themeColorIndicator: {
    flexDirection: 'row',
  },
  colorSwatch: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginLeft: 6,
  },
});

export default ThemeModalOption;
