import {ResultSet, Transaction} from 'react-native-sqlite-storage';
import {db} from '../database';
import {TipWithDetails} from '../../models/types';

// Helper function to map raw DB result to TipWithDetails
const mapRowToTipWithDetails = (row: any): TipWithDetails => {
  const details = row.detail_id
    ? {
        id: row.detail_id,
        url: row.detailUrl,
        summary: row.summary,
        graph: row.graph,
      }
    : null;

  return {
    id: row.id,
    title: row.title,
    subtitle: row.subtitle,
    url: row.url,
    img: row.img || '',
    details: details,
  };
};

/**
 * Fetches all tips from the database with their associated details
 * @returns A Promise that resolves with an array of tips
 */
export const getAllTips = (): Promise<TipWithDetails[]> => {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.transaction((tx: Transaction) => {
      // Use LEFT JOIN to include tips even if they don't have details
      const query = `
        SELECT
          Tips.*,
          Details.id AS detail_id, Details.summary, Details.graph, Details.url AS detailUrl
        FROM Tips
        LEFT JOIN Details ON Tips.detail_id = Details.id
        ORDER BY Tips.id ASC;
      `;

      tx.executeSql(
        query,
        [],
        (_: Transaction, results: ResultSet) => {
          const tips: TipWithDetails[] = [];

          for (let i = 0; i < results.rows.length; i++) {
            const rawTip = results.rows.item(i);
            tips.push(mapRowToTipWithDetails(rawTip));
          }
          console.log(`Retrieved ${tips.length} tips from database`);
          resolve(tips);
        },
        (_, error) => {
          console.error('Error fetching tips:', error);
          reject(error);
          return false;
        }
      );
    });
  });
};
