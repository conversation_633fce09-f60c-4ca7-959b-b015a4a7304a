import React from 'react';
import {View, Image, StyleSheet} from 'react-native';
import {getImageSource} from '../../lib/imgMappings';
import {useTipThemeColors} from './hooks/useTipThemeColors';

interface TipImgProps {
  img: string;
}

const TipImg: React.FC<TipImgProps> = ({img}) => {
  const {imageBackgroundColor, imageBorderColor, imageGlowColor} = useTipThemeColors();

  if (!img) {
    return null;
  }

  return (
    <View style={styles.imageSection}>
      <View
        style={[
          styles.imageContainer,
          {
            borderColor: imageBorderColor,
            backgroundColor: imageBackgroundColor,
          },
          imageGlowColor && {
            shadowColor: imageGlowColor,
            shadowOffset: {width: 0, height: 0},
            shadowOpacity: 0.8,
            shadowRadius: 8,
            elevation: 8,
          },
        ]}>
        <Image
          source={getImageSource(img)}
          style={styles.image}
          resizeMode="cover"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  imageSection: {
    marginBottom: 16,
  },
  imageContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  image: {
    width: '100%',
    height: 220,
    backgroundColor: 'transparent',
  },
});

export default TipImg;
