// Image mappings for tip images
const IMAGE_MAPPING: {[key: string]: any} = {
  'example.png': require('../assets/images/tips-imgs/example.png'),
  '1ae5f895-13f3-4b34-b089-718191cad719.png': require('../assets/images/tips-imgs/1ae5f895-13f3-4b34-b089-718191cad719.png'),
  '6bf3630d-0a9a-47a5-9c69-3878120c2f92.png': require('../assets/images/tips-imgs/6bf3630d-0a9a-47a5-9c69-3878120c2f92.png'),
  '6e82cded-32a9-4349-958d-a434d2b95351.png': require('../assets/images/tips-imgs/6e82cded-32a9-4349-958d-a434d2b95351.png'),
  '33f835f1-4401-43b3-a86f-fad57fefe33c.png': require('../assets/images/tips-imgs/33f835f1-4401-43b3-a86f-fad57fefe33c.png'),
  '4fbaa310-801a-45f7-b188-e07d7588b21e.png': require('../assets/images/tips-imgs/4fbaa310-801a-45f7-b188-e07d7588b21e.png'),
  'd07e0dd5-0eb2-4d51-8f07-48553df2632f.png': require('../assets/images/tips-imgs/d07e0dd5-0eb2-4d51-8f07-48553df2632f.png'),
  '5467ee34-d882-4e4f-8967-172f0ec942f6.png': require('../assets/images/tips-imgs/5467ee34-d882-4e4f-8967-172f0ec942f6.png'),
  '73744cad-d1c4-4eda-bb61-3ac264972bfd.png': require('../assets/images/tips-imgs/73744cad-d1c4-4eda-bb61-3ac264972bfd.png'),
  'ee58f8c3-4376-4396-a825-5f35194ab459.png': require('../assets/images/tips-imgs/ee58f8c3-4376-4396-a825-5f35194ab459.png'),
  '8b7ec7c4-fefa-4b67-82af-14ed322fc751.png': require('../assets/images/tips-imgs/8b7ec7c4-fefa-4b67-82af-14ed322fc751.png'),
  'fa3f9bb9-0e79-41c5-a335-a049378872d4.png': require('../assets/images/tips-imgs/fa3f9bb9-0e79-41c5-a335-a049378872d4.png'),
};

/**
 * Gets the image source for a given image name
 * @param imageName The name of the image to retrieve
 * @returns The image source or null if not found
 */
export const getImageSource = (imageName: string) => {
  if (!imageName) {
    return null;
  }

  try {
    // Use the static mapping to get the image
    if (IMAGE_MAPPING[imageName]) {
      return IMAGE_MAPPING[imageName];
    }
    console.warn(`Image not found in mapping: ${imageName}`);
    return null;
  } catch (error) {
    console.warn(`Failed to load image: ${imageName}`, error);
    return null;
  }
};

export default IMAGE_MAPPING;
