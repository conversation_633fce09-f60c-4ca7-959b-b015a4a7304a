import sqlite3 from 'sqlite3';
import type {Detail} from '../../src/models/types.ts'; // Adjust path as needed
import * as path from 'path';
import * as fs from 'fs';
import {dirname} from 'path'; // Import necessary function
import {fileURLToPath} from 'url'; // Import necessary function
import {finalizeStmt, runStmt} from './databaseUtils.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const detailsJsonPath = path.join(__dirname, 'details.json'); // Path for details.json

const getDetailsData = () => {
  try {
    const detailsJsonData = fs.readFileSync(detailsJsonPath, 'utf-8');
    return JSON.parse(detailsJsonData);
  } catch (error) {
    console.error(
      `Error reading or parsing JSON file at ${detailsJsonPath}:`,
      error,
    );
    process.exit(1); // Exit if data can't be read
  }
};

export async function insertDetails(db: sqlite3.Database): Promise<void> {
  const detailsData: Detail[] = getDetailsData(); // Use Detail type
  const detailsStmt = db.prepare(
    'INSERT INTO Details (id, url, summary, graph) VALUES (?, ?, ?, ?)',
  );

  console.log('Inserting details...');
  for (const detail of detailsData) {
    try {
      await runStmt(detailsStmt, [
        detail.id,
        detail.url,
        detail.summary,
        detail.graph,
      ]);
      // Optional: Log success for each detail if needed
      // console.log(`Inserted detail: ${detail.id}`);
    } catch (runErr: any) {
      console.error(`Error inserting detail "${detail.id}":`, runErr.message);
      throw runErr; // Rethrow error to handle it later
    }
  }
  await finalizeStmt(detailsStmt);
  console.log('Finished inserting details.');
  return;
}
