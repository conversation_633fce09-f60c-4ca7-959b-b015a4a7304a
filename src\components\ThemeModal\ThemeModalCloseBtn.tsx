import React from 'react';
import {TouchableOpacity, Text, StyleSheet} from 'react-native';
import {useThemeModalColors} from './hooks/useThemeModalColors';

interface ThemeModalCloseBtnProps {
  onClose: () => void;
}

const ThemeModalCloseBtn: React.FC<ThemeModalCloseBtnProps> = ({onClose}) => {
  const {optionBackgroundColor, optionTextColor, modalBorderColor} = useThemeModalColors();

  return (
    <TouchableOpacity
      style={[
        styles.closeButton,
        {
          backgroundColor: optionBackgroundColor,
          borderColor: modalBorderColor,
          borderWidth: 1,
        },
      ]}
      onPress={onClose}>
      <Text style={[styles.closeButtonText, {color: optionTextColor}]}>Close</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  closeButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default ThemeModalCloseBtn;
