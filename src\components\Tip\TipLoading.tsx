import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated, Easing, ViewStyle } from 'react-native';
import { useTipThemeColors } from './hooks/useTipThemeColors';

const TipLoading: React.FC = () => {
  const spinAnim = useRef(new Animated.Value(0)).current;
  const { primaryColor, secondaryColor, cardBgColor } = useTipThemeColors();

  useEffect(() => {
    const animation = Animated.loop(
      Animated.timing(spinAnim, {
        toValue: 1,
        duration: 1200,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
    
    animation.start();
    
    return () => {
      animation.stop();
      spinAnim.setValue(0);
    };
  }, [spinAnim]);

  const spin = spinAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Dynamic shadow style for container
  const containerShadow: ViewStyle = {
    shadowColor: primaryColor,
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  };

  return (
    <View
      style={[
        styles.container,
        {
          borderColor: secondaryColor,
          backgroundColor: cardBgColor,
          ...containerShadow,
        },
      ]}
    >
      <Animated.View
        style={[
          styles.spinner,
          {
            borderRightColor: secondaryColor,
            borderTopColor: primaryColor,
            borderLeftColor: primaryColor,
            borderBottomColor: secondaryColor,
            transform: [{ rotate: spin }],
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    marginTop: 10,
    borderWidth: 1,
    minHeight: 250,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  spinner: {
    width: 128,
    height: 128,
    borderWidth: 14,
    borderRadius: 128,
    backgroundColor: 'transparent',
  },
});

export default TipLoading;
