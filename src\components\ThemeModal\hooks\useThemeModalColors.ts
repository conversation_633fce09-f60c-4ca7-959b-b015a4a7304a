import { useTheme } from '../../../theme/ThemeContext';
import Colors from '../../../theme/colors';

export function useThemeModalColors() {
  const { isDarkMode, isNeonMode, isSynthwaveMode } = useTheme();

  if (isSynthwaveMode) {
    return {
      // Modal container
      modalBackgroundColor: Colors.synthwave.surface,
      modalBorderColor: Colors.synthwave.border,
      modalShadowColor: Colors.synthwave.glow.pink,
      
      // Header
      headerTextColor: Colors.synthwave.text,
      
      // Options
      optionTextColor: Colors.synthwave.text,
      optionSelectedColor: Colors.synthwave.accent,
      optionBackgroundColor: Colors.synthwave.surface,
      
      // Close button
      closeButtonColor: Colors.synthwave.textSecondary,
      
      // Shadow properties
      modalShadowOpacity: 0.8,
      modalShadowRadius: 15,
      modalElevation: 15,
    };
  } else if (isNeonMode) {
    return {
      // Modal container
      modalBackgroundColor: Colors.neon.surface,
      modalBorderColor: Colors.neon.border,
      modalShadowColor: Colors.neon.glow.blue,
      
      // Header
      headerTextColor: Colors.neon.text,
      
      // Options
      optionTextColor: Colors.neon.text,
      optionSelectedColor: Colors.neon.accent,
      optionBackgroundColor: Colors.neon.surface,
      
      // Close button
      closeButtonColor: Colors.neon.textSecondary,
      
      // Shadow properties
      modalShadowOpacity: 0.8,
      modalShadowRadius: 15,
      modalElevation: 15,
    };
  } else {
    return {
      // Modal container
      modalBackgroundColor: Colors.light.surface,
      modalBorderColor: Colors.light.border,
      modalShadowColor: Colors.light.shadow,
      
      // Header
      headerTextColor: Colors.light.text,
      
      // Options
      optionTextColor: Colors.light.text,
      optionSelectedColor: Colors.light.accent,
      optionBackgroundColor: Colors.light.surface,
      
      // Close button
      closeButtonColor: Colors.light.textSecondary,
      
      // Shadow properties
      modalShadowOpacity: 0.5,
      modalShadowRadius: 10,
      modalElevation: 10,
    };
  }
}
