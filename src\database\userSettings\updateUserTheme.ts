import {SQLiteDatabase} from 'react-native-sqlite-storage';

/**
 * Updates the theme for a given user in the UserSettings table.
 * @param db The SQLite database instance.
 * @param id The id of the user settings row to update.
 * @param theme The new theme value to set.
 * @returns A promise that resolves when the update is complete.
 */
export function updateUserTheme(
  db: SQLiteDatabase,
  id: number,
  theme: string,
): Promise<void> {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        'UPDATE UserSettings SET theme = ? WHERE id = ?',
        [theme, id],
        () => {
          resolve();
        },
        (_, error) => {
          reject(error);
          return false;
        },
      );
    });
  });
}
