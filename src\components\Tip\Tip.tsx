import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
} from 'react';
import {View, StyleSheet, Linking, Animated, PanResponder} from 'react-native';
import {useTipThemeColors} from './hooks/useTipThemeColors';
import {getFirstTip} from '../../database/tips/getFirstTip';
import {getNextTip} from '../../database/tips/getNextTip';
import {getPreviousTip} from '../../database/tips/getPreviousTip';
import {getTipById} from '../../database/tips/getTipById';
import TipImg from './TipImg';
import TipSubtitle from './TipSubtitle';
import TipTitle from './TipTitle';
import TipUrl from './TipUrl';
import TipSummary from './TipSummary';
import {TipWithDetails} from '../../models/types';
import animateTransition from './animations/animateTransition';
import TipLoading from './TipLoading';

interface TipProps {
  onNext: () => void;
  initialTipId?: number | null;
}

export interface TipRef {
  handleNext: () => void;
  handlePrevious: () => void;
}

const Tip = forwardRef<TipRef, TipProps>(({onNext, initialTipId}, ref) => {
  // Use TipWithDetails for state
  const [tip, setTip] = useState<TipWithDetails | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const {
    borderColor,
    menuGlowColor,
    menuIndicatorShadowOpacity,
    menuIndicatorShadowRadius,
    menuIndicatorElevation,
    cardBgColor,
    detailsBorderTopColor,
  } = useTipThemeColors();

  // Animation values
  const slideAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  // Ref to store current tip to avoid stale closures

  // Pan responder for swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_evt, gestureState) => {
        // Only respond to horizontal swipes with sufficient movement
        return Math.abs(gestureState.dx) > 20 && Math.abs(gestureState.dy) < 80;
      },
      onPanResponderGrant: () => {
        // Gesture started - we can add haptic feedback here if needed
      },
      // onPanResponderMove: (_evt, gestureState) => {
      //   // Update the slide animation based on gesture movement
      //   if (!isAnimating) {
      //     slideAnim.setValue(gestureState.dx * 0.5); // Dampen the movement
      //   }
      // },
      onPanResponderRelease: (_evt, gestureState) => {
        // Determine if swipe was significant enough to trigger navigation
        const swipeThreshold = 100;
        const velocityThreshold = 0.5;

        console.log('isAnimating', isAnimating);
        if (!isAnimating) {
          if (
            gestureState.dx > swipeThreshold ||
            gestureState.vx > velocityThreshold
          ) {
            // Swipe right - go to previous tip
            console.log('Swipe right - calling handlePrevious, tip:');
            handlePrevious();
          } else if (
            gestureState.dx < -swipeThreshold ||
            gestureState.vx < -velocityThreshold
          ) {
            // Swipe left - go to next tip
            console.log('Swipe left - calling handleNext, tip:');
            handleNext();
          } else {
            // Not enough movement, snap back to original position
            // Animated.spring(slideAnim, {
            //   toValue: 0,
            //   useNativeDriver: true,
            //   tension: 100,
            //   friction: 8,
            // }).start();
          }
        } else {
          // If animating or no tip, just snap back
          console.log(
            'Gesture blocked - isAnimating:',
            isAnimating,
            'currentTip:',
          );
        }
      },
    }),
  ).current;

  // Update the ref whenever tip changes
  useEffect(() => {
    console.log(JSON.stringify(tip));
    console.trace();
  }, [tip]);

  useEffect(() => {
    const loadTip = async () => {
      try {
        if (initialTipId) {
          // If initialTipId is provided, fetch that specific tip
          const specificTip = await getTipById(initialTipId);

          if (specificTip) {
            setTip(specificTip);
          } else {
            // Fallback to first tip if the specific tip is not found
            const firstTip = await getFirstTip();
            setTip(firstTip);
          }
        } else {
          // Otherwise, get the first tip as usual
          const firstTip = await getFirstTip();
          setTip(firstTip);
        }
      } catch (error) {
        console.error('Error loading tip:', error);
      }
    };

    loadTip();
  }, [initialTipId]);

  const handleNext = async () => {
    console.log('handleNext called - tip:', tip, 'isAnimating:', isAnimating);
    if (true) {
      try {
        const nextTip = await getNextTip(tip.id);
        if (nextTip) {
          console.log('nextTip found:', nextTip);
          animateTransition({
            direction: 'next',
            newTip: nextTip,
            slideAnim,
            opacityAnim,
            setTip,
            setIsAnimating,
            isAnimating,
          });
        } else {
          console.log('No next tip found');
        }
        // If nextTip is null (end of list reached, wrapped around), getNextTip handles it
      } catch (error) {
        console.error('Error getting next tip:', error);
      }
    } else {
      console.log(
        'handleNext blocked - tip:',
        tip,
        'isAnimating:',
        isAnimating,
      );
    }
  };

  const handlePrevious = async () => {
    console.log(
      'handlePrevious called - tip:',
      tip,
      'isAnimating:',
      isAnimating,
    );
    if (tip && !isAnimating) {
      try {
        const previousTip = await getPreviousTip(tip.id);
        if (previousTip) {
          console.log('previousTip found:', previousTip);
          animateTransition({
            direction: 'previous',
            newTip: previousTip,
            slideAnim,
            opacityAnim,
            setTip,
            setIsAnimating,
            isAnimating,
          });
        } else {
          console.log('No previous tip found');
        }
        // If previousTip is null (start of list reached, wrapped around), getPreviousTip handles it
      } catch (error) {
        console.error('Error getting previous tip:', error);
      }
    } else {
      console.log(
        'handlePrevious blocked - tip:',
        tip,
        'isAnimating:',
        isAnimating,
      );
    }
  };

  const handleOpenUrl = (url: string) => {
    if (url) {
      console.log('url', url);
      Linking.openURL(url);
    }
  };

  useImperativeHandle(ref, () => ({
    handleNext,
    handlePrevious,
  }));

  useEffect(() => {
    if (onNext) {
      onNext();
    }
  }, [tip, onNext]); // Render logic using the tip state (TipWithDetails)
  if (!tip) {
    // Return loading indicator
    return <TipLoading />;
  }

  return (
    <Animated.View
      {...panResponder.panHandlers}
      style={[
        styles.container,
        {
          backgroundColor: cardBgColor,
          borderColor,
          opacity: opacityAnim,
          transform: [{translateX: slideAnim}],
          ...(menuGlowColor && {
            shadowColor: menuGlowColor,
            shadowOffset: {width: 0, height: 0},
            shadowOpacity: menuIndicatorShadowOpacity ?? 0.8,
            shadowRadius: menuIndicatorShadowRadius ?? 10,
            elevation: menuIndicatorElevation ?? 10,
          }),
        },
      ]}>
      <TipTitle title={tip.title} />
      <TipImg img={tip.img} />
      <TipSubtitle subtitle={tip.subtitle} />
      <TipUrl url={tip.url} onPress={() => handleOpenUrl(tip.url)} />
      <View
        style={[
          styles.detailsContainer,
          {
            borderTopColor: detailsBorderTopColor,
          },
        ]}>
        <TipSummary summary={tip.details?.summary} />
        <TipUrl
          url={tip.details?.url}
          onPress={() => tip.details?.url && handleOpenUrl(tip.details.url)}
        />
      </View>
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    marginTop: 10,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailsContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
  },
});

export default Tip;
