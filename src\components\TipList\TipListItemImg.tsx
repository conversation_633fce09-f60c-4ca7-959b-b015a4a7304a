import React, {useState} from 'react';
import {Image, StyleSheet, View, ActivityIndicator} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useTheme} from '../../theme/ThemeContext';
import {useTipListThemeColors} from './hooks/useTipListThemeColors';
import {getImageSource} from '../../lib/imgMappings';

interface TipListItemImgProps {
  imagePath: string;
  size?: number;
}

const TipListItemImg: React.FC<TipListItemImgProps> = ({
  imagePath,
  size = 64,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const colors = useTipListThemeColors();
  const {isNeonMode, isSynthwaveMode} = useTheme();
  // Get the image from imgMappings or use the path directly
  const imageSource = getImageSource(imagePath) || {uri: imagePath};

  const renderPlaceholder = () => {
    if (
      (isSynthwaveMode || isNeonMode) &&
      colors.imagePlaceholderGradient?.colors
    ) {
      return (
        <LinearGradient
          colors={colors.imagePlaceholderGradient.colors}
          start={colors.imagePlaceholderGradient.start}
          end={colors.imagePlaceholderGradient.end}
          style={[
            styles.tipImage,
            {width: size, height: size, borderRadius: size / 2},
          ]}
        />
      );
    } else {
      return (
        <View
          style={[
            styles.tipImage,
            {
              backgroundColor: colors.accentColor,
              width: size,
              height: size,
              borderRadius: size / 2,
            },
          ]}
        />
      );
    }
  };

  // If there's an error or no image path provided, show the placeholder
  if (hasError || !imagePath) {
    return renderPlaceholder();
  }

  return (
    <View style={styles.container}>
      {isLoading && (
        <View style={[styles.loaderContainer, {width: size, height: size}]}>
          <ActivityIndicator size="small" color={colors.accentColor} />
        </View>
      )}{' '}
      <Image
        source={imageSource}
        style={[
          styles.tipImage,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            opacity: isLoading ? 0 : 1,
          },
        ]}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setHasError(true);
          setIsLoading(false);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  tipImage: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  loaderContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TipListItemImg;
