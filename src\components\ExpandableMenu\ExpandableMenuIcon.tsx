import React from 'react';
import {View, StyleSheet} from 'react-native';
import {useExpandableMenuThemeColors} from './hooks/useExpandableMenuThemeColors';

interface ExpandableMenuIconProps {
  iconColor?: string;
}

const ExpandableMenuIcon: React.FC<ExpandableMenuIconProps> = ({iconColor}) => {
  const {primaryColor} = useExpandableMenuThemeColors();
  const color = iconColor || primaryColor;
  return (
    <View style={styles.menuIconContainer}>
      <View style={[styles.menuIconBar, {backgroundColor: color}]} />
      <View style={[styles.menuIconBar, {backgroundColor: color}]} />
      <View style={[styles.menuIconBar, {backgroundColor: color}]} />
    </View>
  );
};

const styles = StyleSheet.create({
  menuIconContainer: {
    width: 20,
    height: 14,
    justifyContent: 'space-between',
  },
  menuIconBar: {
    width: '100%',
    height: 2,
    borderRadius: 4,
  },
});

export default ExpandableMenuIcon;
