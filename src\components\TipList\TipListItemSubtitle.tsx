import React from 'react';
import {Text, StyleSheet} from 'react-native';
import {useTipListThemeColors} from './hooks/useTipListThemeColors';

interface TipListItemSubtitleProps {
  subtitle: string;
}

const TipListItemSubtitle: React.FC<TipListItemSubtitleProps> = ({
  subtitle,
}) => {
  const colors = useTipListThemeColors();

  return (
    <Text
      style={[
        styles.tipSubtitle,
        {
          color: colors.textSecondaryColor,
          ...colors.subtitleTextShadow,
        },
      ]}
      numberOfLines={3}>
      {subtitle}
    </Text>
  );
};

const styles = StyleSheet.create({
  tipSubtitle: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.9,
  },
});

export default TipListItemSubtitle;
