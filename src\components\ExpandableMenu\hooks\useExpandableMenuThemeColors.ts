import { useTheme } from '../../../theme/ThemeContext';
import Colors from '../../../theme/colors';

export function useExpandableMenuThemeColors() {
  const { isDarkMode, isNeonMode, isSynthwaveMode } = useTheme();

  if (isSynthwaveMode) {
    return {
      // Menu container
      backgroundColor: Colors.synthwave.surface,
      borderColor: Colors.synthwave.border,
      shadowColor: Colors.synthwave.glow.pink,

      // Text
      textColor: Colors.synthwave.text,
      textSecondary: Colors.synthwave.textSecondary,

      // Icons
      primaryColor: Colors.synthwave.primary,
      closeIconColor: Colors.synthwave.textSecondary,

      // Menu items
      menuItemBackground: Colors.synthwave.surface,
      menuItemSelected: Colors.synthwave.accent,
      menuGlowColor: Colors.synthwave.glow.blue,

      // Shadows and effects
      menuIndicatorShadowColor: Colors.synthwave.glow.pink,
      menuTextShadowColor: Colors.synthwave.glow.blue,
      menuIndicatorShadowOpacity: 0.8,
      menuIndicatorShadowRadius: 10,
      menuIndicatorElevation: 8,
      menuTextShadowRadius: 2,
    };
  } else if (isNeonMode) {
    return {
      // Menu container
      backgroundColor: Colors.neon.surface,
      borderColor: Colors.neon.border,
      shadowColor: Colors.neon.glow.blue,

      // Text
      textColor: Colors.neon.text,
      textSecondary: Colors.neon.textSecondary,

      // Icons
      primaryColor: Colors.neon.primary,
      closeIconColor: Colors.neon.textSecondary,

      // Menu items
      menuItemBackground: Colors.neon.surface,
      menuItemSelected: Colors.neon.accent,
      menuGlowColor: Colors.neon.glow.blue,

      // Shadows and effects
      menuIndicatorShadowColor: Colors.neon.glow.blue,
      menuTextShadowColor: Colors.neon.glow.blue,
      menuIndicatorShadowOpacity: 0.8,
      menuIndicatorShadowRadius: 10,
      menuIndicatorElevation: 8,
      menuTextShadowRadius: 2,
    };
  } else {
    // Default/light mode
    return {
      // Menu container
      backgroundColor: isDarkMode ? Colors.dark.surface : Colors.light.surface,
      borderColor: isDarkMode ? Colors.dark.border : Colors.light.border,
      shadowColor: isDarkMode ? (Colors.dark as any).shadow : (Colors.light as any).shadow,

      // Text
      textColor: isDarkMode ? Colors.dark.text : Colors.light.text,
      textSecondary: isDarkMode ? Colors.dark.textSecondary : Colors.light.textSecondary,

      // Icons
      primaryColor: isDarkMode ? Colors.dark.primary : Colors.light.primary,
      closeIconColor: isDarkMode ? Colors.dark.textSecondary : Colors.light.textSecondary,

      // Menu items
      menuItemBackground: isDarkMode ? Colors.dark.surface : Colors.light.surface,
      menuItemSelected: isDarkMode ? Colors.dark.accent : Colors.light.accent,
      menuGlowColor: isDarkMode ? Colors.dark.primary : Colors.light.primary,

      // Shadows and effects
      menuIndicatorShadowColor: isDarkMode ? (Colors.dark as any).shadow : (Colors.light as any).shadow,
      menuTextShadowColor: isDarkMode ? (Colors.dark as any).shadow : (Colors.light as any).shadow,
      menuIndicatorShadowOpacity: isDarkMode ? 0.5 : 0.2,
      menuIndicatorShadowRadius: 6,
      menuIndicatorElevation: 4,
      menuTextShadowRadius: 1,
    };
  }
}

export default useExpandableMenuThemeColors;
