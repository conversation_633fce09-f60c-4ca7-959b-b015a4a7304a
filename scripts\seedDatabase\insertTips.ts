import {finalizeStmt, runStmt} from './databaseUtils.ts';
import sqlite3 from 'sqlite3';
import * as fs from 'fs';
import {fileURLToPath} from 'url';
import {dirname} from 'path';
import * as path from 'path';

export const insertTips = async (db: sqlite3.Database) => {
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = dirname(__filename);
  const tipsJsonPath = path.join(__dirname, 'tips.json'); // Assumes tips.json is in the same directory

  const tipsJsonData = fs.readFileSync(tipsJsonPath, 'utf-8');
  const tipsData = JSON.parse(tipsJsonData);
  const tipsStmt = db.prepare(
    'INSERT INTO Tips (title, subtitle, url, img, detail_id) VALUES (?, ?, ?, ?, ?)', // Removed id, changed body to subtitle
  );
  console.log('Inserting tips...');
  for (const tip of tipsData) {
    // Use detail_id directly from the extended Tip type, defaulting to null if not present
    const detail_id = tip.detail_id || null;

    try {
      // Removed tip.id from parameters as it's AUTOINCREMENT
      await runStmt(tipsStmt, [
        tip.title,
        tip.subtitle, // Use subtitle property
        tip.url,
        tip.img,
        detail_id,
      ]);
    } catch (runErr: any) {
      console.error(`Error inserting tip "${tip.title}":`, runErr.message);
      throw runErr; // Rethrow error to handle it later
    }
  }
  await finalizeStmt(tipsStmt);
  console.log('Finished inserting tips.');
};
