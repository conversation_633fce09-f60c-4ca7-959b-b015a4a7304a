import React from 'react';
import {Text, StyleSheet, View} from 'react-native';
import {useTipThemeColors} from './hooks/useTipThemeColors';

interface TipTitleProps {
  title: string;
}

const TipTitle: React.FC<TipTitleProps> = ({title}) => {
  const {textColor, accentColor, menuGlowColor} = useTipThemeColors();

  return (
    <View style={styles.titleContainer}>
      <View
        style={[
          styles.titleBar,
          {backgroundColor: accentColor},
          menuGlowColor && {
            shadowColor: menuGlowColor,
            shadowOffset: {width: 0, height: 0},
            shadowOpacity: 0.8,
            shadowRadius: 5,
            elevation: 5,
          },
        ]}
      />
      <Text
        style={[
          styles.tipTitle,
          {color: textColor},
          menuGlowColor && {
            textShadowColor: menuGlowColor,
            textShadowRadius: 3,
            textShadowOffset: {width: 0, height: 0},
          },
        ]}>
        {title}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  titleContainer: {
    marginBottom: 16,
    position: 'relative',
  },
  titleBar: {
    height: 4,
    width: 40,
    borderRadius: 2,
    marginBottom: 12,
  },
  tipTitle: {
    fontSize: 24,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
});

export default TipTitle;
