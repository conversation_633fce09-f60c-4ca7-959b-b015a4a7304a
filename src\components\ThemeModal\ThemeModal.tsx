import React from 'react';
import {Modal, View, StyleSheet} from 'react-native';
import Colors from '../../theme/colors';
import {useTheme} from '../../theme/ThemeContext';
import {ThemeModeEnum} from '../../models/constants';
import ThemeModalOption from './ThemeModalOption';
import ThemeModalHeader from './ThemeModalHeader';
import ThemeModalCloseBtn from './ThemeModalCloseBtn';
import {useThemeModalColors} from './hooks/useThemeModalColors';

interface ThemeModalProps {
  visible: boolean;
  onClose: () => void;
}

const ThemeModal: React.FC<ThemeModalProps> = ({visible, onClose}) => {
  const {setThemeMode} = useTheme();
  const {modalBackgroundColor, modalBorderColor, modalShadowColor, modalShadowOpacity, modalShadowRadius, modalElevation} = useThemeModalColors();

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}>
      <View style={styles.centeredView}>
        <View
          style={[
            styles.modalView,
            {
              backgroundColor: modalBackgroundColor,
              borderColor: modalBorderColor,
              shadowColor: modalShadowColor,
              shadowOffset: {width: 0, height: 0},
              shadowOpacity: modalShadowOpacity,
              shadowRadius: modalShadowRadius,
              elevation: modalElevation,
            },
          ]}>
          <ThemeModalHeader title="Select Theme" />

          <View style={styles.buttonContainer}>
            <ThemeModalOption
              mode={ThemeModeEnum.LIGHT}
              label="Light Mode"
              backgroundColor={Colors.light.background}
              textColor={Colors.light.text}
              colorSwatches={[
                Colors.light.primary,
                Colors.light.secondary,
                Colors.light.accent,
              ]}
              onSelect={mode => {
                setThemeMode(mode);
                onClose();
              }}
            />

            <ThemeModalOption
              mode={ThemeModeEnum.DARK}
              label="Dark Mode"
              backgroundColor={Colors.dark.background}
              textColor={Colors.dark.text}
              colorSwatches={[
                Colors.dark.primary,
                Colors.dark.secondary,
                Colors.dark.accent,
              ]}
              onSelect={mode => {
                setThemeMode(mode);
                onClose();
              }}
            />

            <ThemeModalOption
              mode={ThemeModeEnum.SYSTEM}
              label="System Default"
              backgroundColor={modalBackgroundColor}
              textColor={Colors.light.text}
              colorSwatches={[
                Colors.light.primary,
                Colors.dark.primary,
                Colors.neon.primary,
                Colors.synthwave.primary,
              ]}
              onSelect={mode => {
                setThemeMode(mode);
                onClose();
              }}
            />

            <ThemeModalOption
              mode={ThemeModeEnum.NEON}
              label="Neon Mode"
              backgroundColor={Colors.neon.background}
              textColor={Colors.neon.text}
              colorSwatches={[
                Colors.neon.primary,
                Colors.neon.secondary,
                Colors.neon.accent,
              ]}
              onSelect={mode => {
                setThemeMode(mode);
                onClose();
              }}
            />

            <ThemeModalOption
              mode={ThemeModeEnum.SYNTHWAVE}
              label="Synthwave"
              backgroundColor={Colors.synthwave.background}
              textColor={Colors.synthwave.text}
              colorSwatches={[
                Colors.synthwave.primary,
                Colors.synthwave.secondary,
                Colors.synthwave.accent,
              ]}
              onSelect={mode => {
                setThemeMode(mode);
                onClose();
              }}
            />
          </View>

          <ThemeModalCloseBtn onClose={onClose} />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    width: '80%',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    borderWidth: 1,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 20,
  },
});

export default ThemeModal;
