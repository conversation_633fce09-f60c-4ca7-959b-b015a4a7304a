import React from 'react';
import {View, StyleSheet} from 'react-native';
import {useExpandableMenuThemeColors} from './hooks/useExpandableMenuThemeColors';

interface ExpandableMenuCloseIconProps {
  iconColor?: string;
}

const ExpandableMenuCloseIcon: React.FC<ExpandableMenuCloseIconProps> = ({
  iconColor,
}) => {
  const {closeIconColor} = useExpandableMenuThemeColors();
  const color = iconColor || closeIconColor;
  return (
    <View style={styles.closeIconContainer}>
      <View
        style={[
          styles.closeIconBar,
          {backgroundColor: color, transform: [{rotate: '45deg'}]},
        ]}
      />
      <View
        style={[
          styles.closeIconBar,
          {backgroundColor: color, transform: [{rotate: '-45deg'}]},
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  closeIconContainer: {
    width: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeIconBar: {
    position: 'absolute',
    width: 18,
    height: 2.5,
    borderRadius: 1,
  },
});

export default ExpandableMenuCloseIcon;
