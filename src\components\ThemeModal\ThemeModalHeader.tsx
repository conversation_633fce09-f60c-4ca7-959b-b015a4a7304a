import React from 'react';
import {Text, StyleSheet} from 'react-native';
import {useThemeModalColors} from './hooks/useThemeModalColors';

interface ThemeModalHeaderProps {
  title: string;
}

const ThemeModalHeader: React.FC<ThemeModalHeaderProps> = ({title}) => {
  const {headerTextColor} = useThemeModalColors();

  return <Text style={[styles.modalTitle, {color: headerTextColor}]}>{title}</Text>;
};

const styles = StyleSheet.create({
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 20,
    textAlign: 'center',
  },
});

export default ThemeModalHeader;
