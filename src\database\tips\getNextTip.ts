import {Transaction} from 'react-native-sqlite-storage';
import {db} from '../database';
import {TipWithDetails, Detail} from '../../models/types'; // Import the correct types

// Helper function to map raw DB result to TipWithDetails (can be shared or redefined)
const mapRowToTipWithDetails = (row: any): TipWithDetails | null => {
  if (!row) {
    return null;
  }
  const details: Detail | null = row.detail_id
    ? {
        id: row.detail_id,
        url: row.detailUrl, // Use the alias
        summary: row.summary,
        graph: row.graph,
      }
    : null;

  return {
    id: row.id,
    title: row.title,
    subtitle: row.subtitle, // Renamed from body
    url: row.url, // Tip's URL
    img: row.img || '',
    details: details,
  };
};

export const getNextTip = async (currentId: number): Promise<TipWithDetails | null> => {
  console.log('Fetching next tip after ID:', currentId);

  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.transaction((tx: Transaction) => {
      // First try to get next tip with higher ID
      const query = `
        SELECT
          Tips.*,
          Details.id AS detail_id, Details.summary, Details.graph, Details.url AS detailUrl
        FROM Tips
        LEFT JOIN Details ON Tips.detail_id = Details.id
        WHERE Tips.id > ?
        ORDER BY Tips.id ASC
        LIMIT 1;
      `;

      tx.executeSql(
        query,
        [currentId],
        (transaction, results) => {
          if (results.rows.length > 0) {
            const rawTip = results.rows.item(0);
            resolve(mapRowToTipWithDetails(rawTip));
          } else {
            // If no next tip, wrap around to the first tip
            const wrapAroundQuery = `
              SELECT
                Tips.*,
                Details.id AS detail_id, Details.summary, Details.graph, Details.url AS detailUrl
              FROM Tips
              LEFT JOIN Details ON Tips.detail_id = Details.id
              ORDER BY Tips.id ASC
              LIMIT 1;
            `;
            tx.executeSql(
              wrapAroundQuery,
              [],
              (innerTx, innerResults) => {
                if (innerResults.rows.length > 0) {
                  const rawTip = innerResults.rows.item(0);
                  resolve(mapRowToTipWithDetails(rawTip));
                } else {
                  resolve(null); // No tips found at all
                }
              },
              (innerTx, error) => {
                console.log('Inner Query Error: ', error);
                reject(error);
                return false;
              }
            );
          }
        },
        (transaction, error) => {
          console.log('Error in getNextTip query: ', error);
          reject(error);
          return false;
        }
      );
    });
  });
};
