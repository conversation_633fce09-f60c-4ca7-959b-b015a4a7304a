import sqlite3 from 'sqlite3';

// Helper function to run prepared statements with promises
export const runStmt = (stmt: sqlite3.Statement, params: any[]) =>
  new Promise<void>((resolve, reject) => {
    stmt.run(params, (runErr: Error | null) => {
      if (runErr) {
        reject(runErr);
      } else {
        resolve();
      }
    });
  });

// Helper function to run SQL commands with promises (needed for transaction)
export const runDb = (
  sql: string,
  db: sqlite3.Database, // Added db parameter
  params: any[] = [], // Added params
) =>
  new Promise<void>((resolve, reject) => {
    db.run(sql, params, (runErr: Error | null) => {
      // Pass params
      if (runErr) {
        reject(runErr);
      } else {
        resolve();
      }
    });
  });
// Helper function to finalize prepared statements with promises
export const finalizeStmt = (stmt: sqlite3.Statement) =>
  new Promise<void>((resolve, reject) => {
    stmt.finalize((finalizeErr: Error | null) => {
      if (finalizeErr) {
        reject(finalizeErr);
      } else {
        resolve();
      }
    });
  });

// Function to close the database connection
export function closeDb(db: sqlite3.Database, message?: string) {
  if (message) {
    console.log(message);
  }
  db.close((closeErr: Error | null) => {
    if (closeErr) {
      console.error('Error closing database:', closeErr.message);
    } else {
      console.log('Database connection closed.');
    }
  });
}
