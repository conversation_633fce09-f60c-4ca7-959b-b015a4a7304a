import {Transaction} from 'react-native-sqlite-storage';
import {db} from '../database';
import {TipWithDetails, Detail} from '../../models/types';

// Helper function to map raw DB result to TipWithDetails
const mapRowToTipWithDetails = (row: any): TipWithDetails | null => {
  if (!row) {
    return null;
  }
  const details: Detail | null = row.detail_id
    ? {
        id: row.detail_id,
        url: row.detailUrl,
        summary: row.summary,
        graph: row.graph,
      }
    : null;

  return {
    id: row.id,
    title: row.title,
    subtitle: row.subtitle,
    url: row.url,
    img: row.img || '',
    details: details,
  };
};

export const getPreviousTip = async (currentId: number): Promise<TipWithDetails | null> => {
  console.log('Fetching previous tip before ID:', currentId);

  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.transaction((tx: Transaction) => {
      // First try to get previous tip with lower ID
      const query = `
        SELECT Tips.*, Details.id AS detail_id, Details.summary, Details.graph, Details.url as detailUrl
        FROM Tips
        LEFT JOIN Details ON Tips.detail_id = Details.id
        WHERE Tips.id < ?
        ORDER BY Tips.id DESC
        LIMIT 1;
      `;

      tx.executeSql(
        query,
        [currentId],
        (transaction, results) => {
          if (results.rows.length > 0) {
            const rawTip = results.rows.item(0);
            resolve(mapRowToTipWithDetails(rawTip));
          } else {
            // If no previous tip, wrap around to the last tip
            const wrapAroundQuery = `
              SELECT Tips.*, Details.id AS detail_id, Details.summary, Details.graph, Details.url as detailUrl
              FROM Tips
              LEFT JOIN Details ON Tips.detail_id = Details.id
              ORDER BY Tips.id DESC
              LIMIT 1;
            `;
            tx.executeSql(
              wrapAroundQuery,
              [],
              (innerTx, innerResults) => {
                if (innerResults.rows.length > 0) {
                  const rawTip = innerResults.rows.item(0);
                  resolve(mapRowToTipWithDetails(rawTip));
                } else {
                  resolve(null); // No tips found at all
                }
              },
              (innerTx, error) => {
                console.log('Inner Query Error: ', error);
                reject(error);
                return false;
              }
            );
          }
        },
        (transaction, error) => {
          console.log('Error in getPreviousTip query: ', error);
          reject(error);
          return false;
        }
      );
    });
  });
};
