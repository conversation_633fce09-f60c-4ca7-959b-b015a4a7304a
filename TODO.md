# TODO List

## UI Components

- [x] expandable menu
- [x] style expandable menu
- [x] fix close menu button needs to be clicked twice
- [x] move close menu button to its own component
- [x] the options on the expandable menu should be a separated component
- [x] components in folders
- [x] improve UI
- [ ] tips as carousel instead of article
- [x] tips list screen
- [x] start screen how to use the app
- [x] theme modal close button should be a component
- [x] animations

## Theming

- [x] options to switch themes
- [x] theme option should be a component
- [x] dark theme
- [x] neon theme
- [x] synthwave theme
- [x] save theme preference to db
- [x] themes names should be on constants
- [x] improve colors/theme changing in src\screens\WelcomeScreen.tsx
- [x] improve colors/theme changing in components\WelcomeScreen\FeatureCard.tsx
- [x] improve colors/theme changing in components\TipList\TipListItem.tsx
- [x] each component folder should have its own useThemeColor hook

## Tip Component

- [x] rename tip.body to tip.subtitle
- [x] style subtitle
- [x] move tip title
- [x] move tip body
- [x] move tip url
- [x] src\components\Tip.tsx settip should be a single function
- [x] update TipWithDetail type
- [x] add summary to all tips
- [x] show studies summary
- [ ] add graph to all tips
- [ ] show graphs
- [ ] rate tips (like, not like, wrong info, broken link)
- [ ] swiping left and right should also change the tip
- [ ] tip text styling, like bold keywords
- [x] components\TipList\TipListItem needs to be separated
- [ ] components\TipList\Tip needs to be separated
- [ ] reduce conditions (ifs) in Tip.tsx when loading first tip

## Database

- [x] sqlite persistent
- [ ] delete db if exists
- [ ] seed database script should also create the .db file
- [x] check why the db isnt updating after running seed and npm run android
- [ ] add graphs to db
- [ ] add studies summary to db
- [x] table for details.json
- [x] another json for the study summary, link, and graph
- [ ] move database out of repository
- [ ] shorter tips summaries like bullet points for carousel
- [ ] remove unused files, functions in src\database\tips
- [ ] move each table creation script to a separated file

## Images

- [x] images
- [x] add images to tips
- [x] generate images for each tip
- [x] fix getImageSource function
- [x] move images to images hosting
- [ ] move image after resizing and compressing
- [ ] move images to image storage
- [ ] better images, comic style
- [ ] tip thumbnail for all tips screen
- [ ] script to get images from hosting

## Scripts

- [x] add script to resize images
- [x] add script to compress images
- [x] scripts to populate the database
- [ ] script to rename image with uuid
- [ ] scripts rename, resize and compress should run one after another
- [ ] colored console logs for scripts
- [ ] readme for each script
- [ ] scripts should have their own package.json
- [ ] script or package to check files for complexity or size

## Code Quality & Organization

- [x] create file for constants
- [x] move to constants const database_name = 'TipsDatabase.db';
- [x] move to constantsexport enum ScreenNames
- [ ] menu options should be on constants
- [ ] available themes should be on constants
- [ ] space in imports
- [ ] eslint project
- [ ] prettier project
- [ ] add test config
- [ ] tipcard in TipListItem should be a separated component
- [ ] ImagePlaceholder in TipListItem should be a separated component
- [ ] each component should have its own types file
- [ ] remove callbacks from tips functions
- [ ] husky on push
- [ ] mapRowToTipWithDetails should be a separated function

## App Identity

- [ ] app name
- [ ] logo
- [ ] disclaimer this is not a medic

## Features

- [ ] send comments, doubts, complains
- [ ] send notification on daily tip
- [ ] ads

## User settings

- [ ] new screen to change user settings
- [ ] theme
- [ ] font size
- [ ] show welcome screen
- [ ] save to user settings the selected theme

## Deployment

- [ ] publish to app store
- [ ] install on phone

## Errors/Warnings

- [ ] on all tips screen there is an error when pressing back

## Expandable menu

- [x] add welcome screen to navigation
- [ ] swiping left should close the menu
- [ ] move some elements out, file too large

## Welcome screen

- [ ] add disclaimer
- [ ] checkbox dont show this again
- [ ] save to user preferences if the user dont want to see it
- [ ] welcome screen features should be on a DB table
- [ ] add synthwave to welcome screen

## Backend

- [ ] add backend
- [ ] handle change theme
- [ ] analytics
