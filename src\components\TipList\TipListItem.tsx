import React from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {TipWithDetails} from '../../models/types';
import LinearGradient from 'react-native-linear-gradient';
import {useTipListThemeColors} from './hooks/useTipListThemeColors';
import {useTheme} from '../../theme/ThemeContext';
import TipListItemTitle from './TipListItemTitle';
import TipListItemSubtitle from './TipListItemSubtitle';
import TipListItemImg from './TipListItemImg';

// Props for the TipListItem component
interface TipListItemProps {
  item: TipWithDetails;
  onPress?: (tipId: number) => void;
}

// TipCard component type definition
type TipCardProps = {
  tip: TipWithDetails;
  onPress?: (tipId: number) => void;
};

// TipCard component for displaying individual tips in the list
const TipCard = ({tip, onPress}: TipCardProps) => {
  const colors = useTipListThemeColors();
  const {isNeonMode, isSynthwaveMode} = useTheme();

  const handlePress = () => {
    if (onPress) {
      onPress(tip.id);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.tipCard,
        {
          backgroundColor: colors.cardBackgroundColor,
          borderColor: colors.cardBorderColor,
          borderWidth: colors.cardBorderWidth,
        },
        colors.cardShadow,
      ]}
      onPress={handlePress}
      activeOpacity={0.6}>
      {(isNeonMode || isSynthwaveMode) && colors.gradientColors && (
        <LinearGradient
          colors={colors.gradientColors}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={StyleSheet.absoluteFillObject}
        />
      )}
      <View style={styles.tipCardContent}>
        <TipListItemTitle title={tip.title} />
        <TipListItemSubtitle subtitle={tip.subtitle} />
      </View>
      <View style={styles.tipImageContainer}>
        <TipListItemImg imagePath={tip.img} />
      </View>
    </TouchableOpacity>
  );
};

const TipListItem: React.FC<TipListItemProps> = ({item, onPress}) => {
  return <TipCard tip={item} onPress={onPress} />;
};

const styles = StyleSheet.create({
  tipCard: {
    flexDirection: 'row',
    borderRadius: 16,
    marginBottom: 16,
    padding: 16,
    borderWidth: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  tipCardContent: {
    flex: 1,
    marginRight: 12,
  },
  tipImageContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TipListItem;
