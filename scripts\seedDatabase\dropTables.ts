import sqlite3 from 'sqlite3';
import {runDb} from './databaseUtils.ts';

export async function dropTables(db: sqlite3.Database): Promise<void> {
  console.log('Dropping existing tables if they exist...');
  try {
    await runDb('DROP TABLE IF EXISTS Details', db);
    console.log("Table 'Details' dropped if it existed.");
    await runDb('DROP TABLE IF EXISTS Tips', db);
    console.log("Table 'Tips' dropped if it existed.");
    await runDb('DROP TABLE IF EXISTS UserSettings', db);
    console.log("Table 'UserSettings' dropped if it existed.");
    console.log('Finished dropping tables.');
  } catch (error: any) {
    console.error('Error dropping tables:', error.message);
    throw error;
  }
}
