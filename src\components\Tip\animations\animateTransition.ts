import { Animated, Easing } from 'react-native';
import { TipWithDetails } from '../../../models/types';

export type AnimateTransitionParams = {
  direction: 'next' | 'previous';
  newTip: TipWithDetails;
  slideAnim: Animated.Value;
  opacityAnim: Animated.Value;
  setTip: (tip: TipWithDetails) => void;
  setIsAnimating: (animating: boolean) => void;
  isAnimating: boolean;
};

const animateTransition = ({
  direction,
  newTip,
  slideAnim,
  opacityAnim,
  setTip,
  setIsAnimating,
  isAnimating,
}: AnimateTransitionParams) => {
  if (isAnimating) {
    return;
  }

  setIsAnimating(true);
  const slideDirection = direction === 'next' ? 300 : -300;

  // Slide out current tip
  Animated.parallel([
    Animated.timing(slideAnim, {
      toValue: -slideDirection,
      duration: 200,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }),
    Animated.timing(opacityAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }),
  ]).start(() => {
    // Update tip and reset position for slide in
    setTip(newTip);
    slideAnim.setValue(slideDirection);

    // Slide in new tip
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsAnimating(false);
    });
  });
};

export default animateTransition;
